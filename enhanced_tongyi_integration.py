#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版通义千问集成模块
集成元数据管理功能，提供更准确的数据分析
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai.llm.base import LLM
import requests
import re
from metadata_manager import metadata_manager
from typing import Optional, Dict, Any
import logging

load_dotenv()
logger = logging.getLogger(__name__)

class EnhancedTongyiQianwenLLM(LLM):
    """增强版通义千问LLM，集成元数据功能"""
    
    def __init__(self, model="qwen-plus", enable_metadata=True):
        """
        初始化增强版LLM
        
        Args:
            model: 模型名称
            enable_metadata: 是否启用元数据功能
        """
        self.api_key = os.getenv('DASHSCOPE_API_KEY')
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        self.enable_metadata = enable_metadata
        self.current_table_name = None
        self.current_dataframe = None
        
    def set_current_data(self, table_name: str, df: pd.DataFrame):
        """
        设置当前分析的数据
        
        Args:
            table_name: 表格名称
            df: DataFrame对象
        """
        self.current_table_name = table_name
        self.current_dataframe = df
        
        # 如果启用元数据且表格未注册，自动注册
        if self.enable_metadata:
            if not metadata_manager.get_table_metadata(table_name):
                logger.info(f"自动注册表格元数据: {table_name}")
                metadata_manager.register_table(table_name, df)
    
    def call(self, instruction, value):
        """
        调用LLM生成代码，集成元数据上下文
        
        Args:
            instruction: 用户指令
            value: 数据信息
            
        Returns:
            str: 生成的Python代码
        """
        # 构建增强的提示词
        enhanced_prompt = self._build_enhanced_prompt(instruction, value)
        
        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        data = {
            "model": self.model, 
            "messages": [{"role": "user", "content": enhanced_prompt}], 
            "temperature": 0.1, 
            "max_tokens": 800  # 增加token限制以支持更复杂的分析
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                code = response.json()['choices'][0]['message']['content']
                
                # 清理代码
                code = self.clean_code(code)
                return code
            else:
                logger.error(f"API调用失败: {response.status_code}")
                return "print('API调用失败')"
        except Exception as e:
            logger.error(f"API异常: {e}")
            return f"print('API异常: {e}')"
    
    def _build_enhanced_prompt(self, instruction: str, value: str) -> str:
        """
        构建增强的提示词，包含元数据信息
        
        Args:
            instruction: 用户指令
            value: 数据信息
            
        Returns:
            str: 增强的提示词
        """
        base_prompt = f"""你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
{value}

用户指令: {instruction}
"""
        
        # 添加元数据上下文
        if self.enable_metadata and self.current_table_name and self.current_dataframe is not None:
            metadata_context = self._get_metadata_context()
            if metadata_context:
                base_prompt += f"""

数据元数据信息:
{metadata_context}

请根据以上元数据信息更准确地理解列名含义和业务逻辑。
"""
        
        # 添加代码生成要求
        base_prompt += """
要求:
1. 只返回可执行的Python代码
2. 使用变量名df表示DataFrame，df已经存在，直接使用即可
3. 绝对不要包含创建DataFrame的代码，如pd.read_csv()、pd.read_excel()等
4. 绝对不要包含任何文件读取操作，数据已经在df变量中
5. 不要包含任何中文注释或解释
5. 使用print()输出最终结果，特别注意：
   - 对于"哪个/哪种...最高/最低"类型查询，必须输出：
     a) 所有项目的统计数据：print(grouped_data)
     b) 明确的答案：print(f"答案: 具体项目名称, 具体指标: 具体数值")
   - 对于比较分析，必须输出完整数据和结论
   - 不能只计算结果而不输出，每个分析都必须有print语句
6. 根据元数据信息理解列的业务含义，生成更准确的分析代码
7. 如果涉及计算，请考虑列之间的业务关系
8. 如果需要生成图表，请遵循以下规则：
   - 导入: import matplotlib.pyplot as plt
   - 设置图表大小: plt.figure(figsize=(12, 8))
   - 必须包含标题: plt.title('图表标题', fontsize=16, fontweight='bold')
   - 必须包含轴标签: plt.xlabel('X轴标签', fontsize=12), plt.ylabel('Y轴标签', fontsize=12)
   - 如果X轴标签较长，使用: plt.xticks(rotation=45, ha='right')
   - 如果有多个数据系列，必须包含图例: plt.legend(fontsize=10)
   - 添加网格线: plt.grid(True, alpha=0.3)
   - 绘图后必须使用: plt.tight_layout() 然后 save_chart()
   - 不要使用 plt.show() 或 plt.close()
   - 图表会自动在前端显示
   - 对于饼图，必须包含数据输出和save_chart()调用:
     ```python
     data = df.groupby('分组列')['数值列'].sum()
     print("数据分布:")
     print(data)
     plt.pie(data.values, labels=data.index, autopct='%1.1f%%', startangle=90)
     plt.title('饼图标题', fontsize=16, fontweight='bold')
     plt.axis('equal')
     plt.tight_layout()
     save_chart()
     ```

代码:"""
        
        return base_prompt
    
    def _get_metadata_context(self) -> Optional[str]:
        """
        获取当前表格的元数据上下文
        
        Returns:
            str: 元数据上下文字符串
        """
        try:
            if not self.current_table_name or self.current_dataframe is None:
                return None
            
            # 生成元数据上下文
            context = metadata_manager.generate_llm_context(
                self.current_table_name, 
                self.current_dataframe
            )
            
            return context
            
        except Exception as e:
            logger.error(f"获取元数据上下文失败: {e}")
            return None
    
    def clean_code(self, code):
        """清理生成的代码"""
        # 移除markdown标记
        code = re.sub(r'```python\n?', '', code)
        code = re.sub(r'```\n?', '', code)
        
        # 按行处理
        lines = code.split('\n')
        clean_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 跳过中文解释行
            if self.contains_chinese_explanation(line):
                continue
                
            # 跳过注释行（但保留代码中的英文注释）
            if line.startswith('#') and any(ord(c) > 127 for c in line):
                continue
                
            clean_lines.append(line)
        
        return '\n'.join(clean_lines).strip()
    
    def contains_chinese_explanation(self, line):
        """检查是否包含中文解释"""
        # 检查是否包含中文标点符号
        chinese_punctuation = '。，：；！？""''（）【】'
        if any(p in line for p in chinese_punctuation):
            return True
            
        # 检查是否以中文开头且不是代码
        if line and ord(line[0]) > 127 and not any(op in line for op in ['=', '(', '[', 'df', 'print']):
            return True
            
        return False
    
    @property
    def type(self):
        return "enhanced_tongyi_qianwen"

def analyze_data_with_metadata(df: pd.DataFrame, query: str, table_name: str = "data_table"):
    """
    使用增强版LLM分析数据，包含元数据支持
    
    Args:
        df: DataFrame对象
        query: 查询语句
        table_name: 表格名称
        
    Returns:
        dict: 分析结果
    """
    import io
    import sys
    from contextlib import redirect_stdout, redirect_stderr

    # 创建结果字典
    result = {
        'query': query,
        'table_name': table_name,
        'data_shape': df.shape,
        'code': '',
        'output': '',
        'error': None,
        'success': False,
        'chart_path': None,
        'chart_figure': None,
        'has_chart': False,
        'metadata_used': False
    }

    print(f"🔍 查询: {query}")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 表格名称: {table_name}")

    try:
        # 创建增强版LLM
        llm = EnhancedTongyiQianwenLLM()
        
        # 设置当前数据
        llm.set_current_data(table_name, df)
        result['metadata_used'] = llm.enable_metadata
        
        # 生成代码
        code = llm.call(query, df.to_string())
        result['code'] = code

        print(f"📝 生成的代码:")
        print(code)
        print(f"🚀 执行结果:")

        # 执行代码的逻辑与原版本相同
        # [这里可以复用原来的执行逻辑]
        
        # 简化版执行逻辑
        output_buffer = io.StringIO()
        error_buffer = io.StringIO()
        
        # 创建DataFrame副本，避免修改原始数据
        df_copy = df.copy()

        exec_globals = {
            'df': df_copy,  # 使用副本而不是原始DataFrame
            'pd': pd,
            'print': lambda *args, **kwargs: print(*args, **kwargs, file=output_buffer)
        }
        
        with redirect_stdout(output_buffer), redirect_stderr(error_buffer):
            exec(code, exec_globals)
        
        output = output_buffer.getvalue()
        error_output = error_buffer.getvalue()
        
        if error_output:
            result['error'] = error_output
            print(f"❌ 执行警告: {error_output}")
        
        if output:
            result['output'] = output
            print(output)
        
        result['success'] = True
        print("✅ 执行成功")
        
        # 显示元数据使用情况
        if result['metadata_used']:
            print("🎯 已使用元数据增强分析准确性")

    except Exception as e:
        result['error'] = str(e)
        result['success'] = False
        print(f"❌ 执行失败: {e}")

    print("-" * 50)
    return result
