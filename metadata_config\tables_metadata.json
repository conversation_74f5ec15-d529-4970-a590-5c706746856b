{"sales_data": {"table_name": "sales_data", "description": "sales_data数据表，包含7个字段和5条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"订单ID": {"name": "订单ID", "display_name": "订单ID", "description": "订单ID字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["ORD001", "ORD002", "ORD003"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T21:13:35.923189", "updated_at": "2025-08-03T21:13:35.923189"}, "客户名称": {"name": "客户名称", "display_name": "客户名称", "description": "客户名称字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["张三公司", "李四企业", "王五集团"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T21:13:35.923189", "updated_at": "2025-08-03T21:13:35.923189"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑", "平板电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T21:13:35.923189", "updated_at": "2025-08-03T21:13:35.923189"}, "销售金额": {"name": "销售金额", "display_name": "销售金额", "description": "销售金额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8500", "6200", "3200"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T21:13:35.923189", "updated_at": "2025-08-03T21:13:35.923189"}, "销售数量": {"name": "销售数量", "display_name": "销售数量", "description": "销售数量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["2", "3", "5"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T21:13:35.923189", "updated_at": "2025-08-03T21:13:35.923189"}, "销售日期": {"name": "销售日期", "display_name": "销售日期", "description": "销售日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-15", "2024-01-16", "2024-01-17"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T21:13:35.923189", "updated_at": "2025-08-03T21:13:35.923189"}, "销售区域": {"name": "销售区域", "display_name": "销售区域", "description": "销售区域字段，表示地理位置或区域信息，每个值都是唯一的", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-03T21:13:35.923189", "updated_at": "2025-08-03T21:13:35.923189"}}, "relationships": {}, "primary_keys": ["订单ID", "客户名称"], "created_at": "2025-08-03T21:13:35.923189", "updated_at": "2025-08-03T21:13:35.923189", "version": "1.0.0"}, "sales_data.csv": {"table_name": "sales_data.csv", "description": "sales_data.csv数据表，包含7个字段和5条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"订单ID": {"name": "订单ID", "display_name": "订单ID", "description": "订单ID字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["ORD001", "ORD002", "ORD003"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T21:15:57.167435", "updated_at": "2025-08-03T21:15:57.167435"}, "客户名称": {"name": "客户名称", "display_name": "客户名称", "description": "客户名称字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["张三公司", "李四企业", "王五集团"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T21:15:57.167435", "updated_at": "2025-08-03T21:15:57.167435"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑", "平板电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T21:15:57.167435", "updated_at": "2025-08-03T21:15:57.167435"}, "销售金额": {"name": "销售金额", "display_name": "销售金额", "description": "销售金额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8500", "6200", "3200"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T21:15:57.167435", "updated_at": "2025-08-03T21:15:57.167435"}, "销售数量": {"name": "销售数量", "display_name": "销售数量", "description": "销售数量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["2", "3", "5"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T21:15:57.167435", "updated_at": "2025-08-03T21:15:57.167435"}, "销售日期": {"name": "销售日期", "display_name": "销售日期", "description": "销售日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-15", "2024-01-16", "2024-01-17"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T21:15:57.167435", "updated_at": "2025-08-03T21:15:57.167435"}, "销售区域": {"name": "销售区域", "display_name": "销售区域", "description": "销售区域字段，表示地理位置或区域信息，每个值都是唯一的", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-03T21:15:57.167435", "updated_at": "2025-08-03T21:15:57.167435"}}, "relationships": {}, "primary_keys": ["订单ID", "客户名称"], "created_at": "2025-08-03T21:15:57.167435", "updated_at": "2025-08-03T21:15:57.167435", "version": "1.0.0"}}