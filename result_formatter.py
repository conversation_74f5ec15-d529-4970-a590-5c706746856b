#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的结果格式化模块
基于PandasAI输出分析，提供最佳的Streamlit显示体验
"""

import streamlit as st
import pandas as pd
import re
from io import StringIO
import numpy as np
from pathlib import Path

class EnhancedResultFormatter:
    """增强的结果格式化器"""

    @staticmethod
    def format_and_display_result(result):
        """格式化并直接显示结果 - 主入口函数"""
        if not result or not result.get('success'):
            st.error("❌ 分析未成功完成")
            return

        output = result.get('output', '')
        if not output:
            st.info("ℹ️ 分析完成，但没有输出内容")
            return

        # 检测输出类型并选择最佳显示方式
        output_type = EnhancedResultFormatter._detect_output_type(output)

        # 根据类型选择显示方法
        if output_type == 'dataframe_info':
            EnhancedResultFormatter._display_dataframe_info(output, result)
        elif output_type == 'statistics_summary':
            EnhancedResultFormatter._display_statistics_summary(output, result)
        elif output_type == 'single_number':
            EnhancedResultFormatter._display_single_number(output, result)
        elif output_type == 'tabular_data':
            EnhancedResultFormatter._display_tabular_data(output, result)
        elif output_type == 'series_data':
            EnhancedResultFormatter._display_series_data(output, result)
        elif output_type == 'mixed_data_with_answer':
            EnhancedResultFormatter._display_mixed_data_with_answer(output, result)
        elif output_type == 'correlation_matrix':
            EnhancedResultFormatter._display_correlation_matrix(output, result)
        else:
            # 默认文本显示，但格式化
            EnhancedResultFormatter._display_formatted_text(output, result)

        # 显示用户生成的图表（如果有且不是Plotly原生图表）
        # 注意：Plotly图表通过st.plotly_chart()直接显示，无需额外处理
        if result.get('has_chart') and not result.get('uses_plotly_native'):
            EnhancedResultFormatter._display_user_chart(result)

    @staticmethod
    def _detect_output_type(output):
        """检测输出类型"""
        lines = output.strip().split('\n')

        # DataFrame信息检测 - 增强检测
        dataframe_keywords = ['DataFrame', 'RangeIndex', 'Data columns', 'entries', 'Non-Null Count', 'Dtype', 'dtypes:', 'memory usage']
        if any(keyword in output for keyword in dataframe_keywords):
            # 进一步检查是否包含列信息格式
            if 'entries' in output or 'Non-Null Count' in output or 'Data columns' in output:
                return 'dataframe_info'

        # 统计摘要检测 (describe输出)
        if any(word in output for word in ['count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max']):
            # 进一步检查是否是标准的describe输出格式
            if len([line for line in lines if any(stat in line for stat in ['count', 'mean', 'std'])]) >= 2:
                return 'statistics_summary'

        # 相关性矩阵检测
        if '价格' in output and '销量' in output and any(word in output for word in ['1.000000', '-0.', '0.']):
            # 检查是否有矩阵格式
            matrix_lines = [line for line in lines if line.strip() and any(char.isdigit() for char in line)]
            if len(matrix_lines) >= 3:  # 至少3行数据
                return 'correlation_matrix'

        # 单一数值检测
        if len(lines) == 1 and lines[0].strip():
            try:
                float(lines[0].strip())
                return 'single_number'
            except:
                pass

        # 混合输出检测（包含表格数据和答案）
        has_tabular = False
        has_answer = False

        for line in lines:
            # 检查是否包含表格数据
            if re.match(r'^\d+\s+\S+\s+[\d.-]+', line.strip()):
                has_tabular = True
            # 检查是否包含明确答案
            if any(keyword in line for keyword in ['答案:', '最高', '最低', '最大', '最小']):
                has_answer = True

        if has_tabular and has_answer:
            return 'mixed_data_with_answer'
        elif has_tabular:
            return 'series_data'

        # 序列数据检测 (如 groupby 结果) - 增强检测
        # 检查是否是DataFrame输出格式（带索引的表格）
        dataframe_output_pattern = False
        if len(lines) >= 3:
            # 检查第一行是否是列名
            first_line = lines[0].strip()
            if any(col in first_line for col in ['产品名称', '地区', '类别', '销售员']) and any(col in first_line for col in ['销售额', '销量', '数量', '金额']):
                # 检查后续行是否是 "索引 值1 值2" 格式
                data_lines = 0
                for line in lines[1:]:
                    line = line.strip()
                    if line and re.match(r'^\d+\s+\S+\s+[\d.-]+', line):
                        data_lines += 1

                if data_lines >= 2:
                    dataframe_output_pattern = True

        # 传统序列数据检测
        series_pattern_count = 0
        for line in lines:
            if re.match(r'^[^\d\s]+\s+[\d.-]+$', line.strip()):
                series_pattern_count += 1

        if dataframe_output_pattern or series_pattern_count >= 2:
            return 'series_data'

        # 表格数据检测 - 降低优先级，避免与series_data冲突
        if len(lines) >= 3 and not dataframe_output_pattern:
            # 检查是否有表头和数据行
            potential_header = lines[0].strip()
            if len(potential_header.split()) >= 2:
                # 检查后续行是否有相似结构
                similar_structure_count = 0
                header_parts = len(potential_header.split())

                for line in lines[1:min(5, len(lines))]:
                    line_parts = len(line.strip().split())
                    if abs(line_parts - header_parts) <= 1:
                        similar_structure_count += 1

                if similar_structure_count >= 2:
                    return 'tabular_data'

        return 'text'

    @staticmethod
    def _display_dataframe_info(output, result):
        """显示DataFrame信息 - 支持混合输出（describe + info）"""
        st.subheader("📊 数据集概览")

        # 分离describe和info输出
        lines = output.split('\n')

        # 查找describe输出部分
        describe_start = -1
        describe_end = -1
        info_start = -1
        info_end = -1

        for i, line in enumerate(lines):
            if 'count' in line and 'mean' in line:
                describe_start = i - 1 if i > 0 else i
            elif describe_start != -1 and describe_end == -1 and line.strip() == '':
                describe_end = i
            elif 'DataFrame' in line and 'RangeIndex' in line:
                info_start = i
            elif info_start != -1 and info_end == -1 and ('memory usage' in line or 'dtypes:' in line):
                # 找到info结束位置
                for j in range(i, min(i + 5, len(lines))):
                    if 'memory usage' in lines[j]:
                        info_end = j + 1
                        break

        # 显示describe结果（如果存在）
        if describe_start != -1 and describe_end != -1:
            describe_output = '\n'.join(lines[describe_start:describe_end])
            if describe_output.strip():
                st.subheader("📈 统计摘要")
                try:
                    # 尝试解析为表格
                    from io import StringIO
                    df_stats = pd.read_csv(StringIO(describe_output), sep='\s+', index_col=0)
                    st.dataframe(df_stats, use_container_width=True)
                except:
                    # 如果解析失败，显示原始文本
                    st.code(describe_output, language='text')

        # 显示info结果
        info_data = {}
        column_info = []

        if info_start != -1:
            info_lines = lines[info_start:info_end] if info_end != -1 else lines[info_start:]

            # 解析基本信息
            for line in info_lines:
                if 'entries' in line:
                    match = re.search(r'(\d+) entries', line)
                    if match:
                        info_data['rows'] = int(match.group(1))
                elif 'Data columns' in line:
                    match = re.search(r'(\d+) columns', line)
                    if match:
                        info_data['columns'] = int(match.group(1))
                elif 'memory usage' in line:
                    match = re.search(r'memory usage: ([\d.]+\+?\s*\w+)', line)
                    if match:
                        info_data['memory'] = match.group(1)

            # 解析列信息
            in_column_section = False
            for line in info_lines:
                if 'Column' in line and 'Non-Null Count' in line and 'Dtype' in line:
                    in_column_section = True
                    continue
                elif in_column_section and line.strip() and not line.startswith('dtypes:'):
                    # 解析列信息行
                    parts = line.strip().split()
                    if len(parts) >= 4:
                        try:
                            col_index = parts[0]
                            col_name = parts[1]
                            non_null_count = parts[2]
                            dtype = parts[-1]
                            column_info.append({
                                'Index': col_index,
                                'Column': col_name,
                                'Non-Null Count': non_null_count,
                                'Dtype': dtype
                            })
                        except:
                            pass
                elif in_column_section and (line.startswith('dtypes:') or not line.strip()):
                    break

        # 显示基本信息指标
        if info_data:
            st.subheader("📊 数据基本信息")
            col1, col2, col3 = st.columns(3)

            with col1:
                if 'rows' in info_data:
                    st.metric("📏 数据行数", f"{info_data['rows']:,}")

            with col2:
                if 'columns' in info_data:
                    st.metric("📋 数据列数", info_data['columns'])

            with col3:
                if 'memory' in info_data:
                    st.metric("💾 内存使用", info_data['memory'])

        # 显示列信息表格
        if column_info:
            st.subheader("📋 列信息详情")
            df_columns = pd.DataFrame(column_info)
            st.dataframe(df_columns, use_container_width=True, hide_index=True)

        # 详细信息可展开查看
        with st.expander("🔍 完整输出", expanded=False):
            st.code(output, language='text')

    @staticmethod
    def _display_statistics_summary(output, result):
        """显示统计摘要"""
        st.subheader("📈 统计摘要")

        try:
            # 尝试解析为DataFrame
            lines = output.strip().split('\n')

            # 找到数据开始的行
            data_start = 0
            for i, line in enumerate(lines):
                if any(stat in line for stat in ['count', 'mean', 'std']):
                    data_start = i
                    break

            # 提取数据行
            data_lines = lines[data_start:]

            # 解析统计数据
            stats_data = {}
            for line in data_lines:
                parts = line.strip().split()
                if len(parts) >= 2 and parts[0] in ['count', 'mean', 'std', 'min', '25%', '50%', '75%', 'max']:
                    stat_name = parts[0]
                    values = [float(x) for x in parts[1:] if x.replace('.', '').replace('-', '').isdigit()]
                    if values:
                        stats_data[stat_name] = values

            if stats_data:
                # 创建DataFrame显示
                df_stats = pd.DataFrame(stats_data).T
                st.dataframe(df_stats, use_container_width=True)
            else:
                # 如果解析失败，显示原始文本
                st.text(output)

        except Exception:
            # 解析失败时显示原始文本
            st.text(output)

    @staticmethod
    def _display_single_number(output, result):
        """显示单一数值结果"""
        try:
            value = float(output.strip())

            # 根据查询内容推断数值类型
            query = result.get('query', '').lower()

            if any(word in query for word in ['总', '和', 'sum', '合计']):
                label = "📊 总计"
            elif any(word in query for word in ['平均', 'mean', '均值']):
                label = "📊 平均值"
            elif any(word in query for word in ['最大', 'max', '最高']):
                label = "📊 最大值"
            elif any(word in query for word in ['最小', 'min', '最低']):
                label = "📊 最小值"
            elif any(word in query for word in ['相关', 'corr', '关联']):
                label = "📊 相关系数"
            else:
                label = "📊 计算结果"

            # 使用大数字显示
            st.metric(label, f"{value:,.4f}".rstrip('0').rstrip('.'))

            # 添加解释
            if 'corr' in query or '相关' in query:
                if abs(value) > 0.7:
                    st.success("💡 强相关关系")
                elif abs(value) > 0.3:
                    st.info("💡 中等相关关系")
                else:
                    st.warning("💡 弱相关关系")

        except ValueError:
            st.text(output)

    @staticmethod
    def _display_tabular_data(output, result):
        """显示表格数据"""
        st.subheader("📋 数据表格")

        try:
            # 尝试解析为DataFrame
            lines = output.strip().split('\n')

            # 找到表头
            header_line = None
            data_start = 0

            for i, line in enumerate(lines):
                if line.strip() and not line.strip().isdigit():
                    # 检查是否像表头
                    parts = line.strip().split()
                    if len(parts) >= 2:
                        header_line = line.strip()
                        data_start = i + 1
                        break

            if header_line and data_start < len(lines):
                # 解析表头
                headers = header_line.split()

                # 解析数据行
                data_rows = []
                for line in lines[data_start:]:
                    if line.strip():
                        parts = line.strip().split()
                        if len(parts) >= len(headers):
                            data_rows.append(parts[:len(headers)])

                if data_rows:
                    # 创建DataFrame
                    df = pd.DataFrame(data_rows, columns=headers)

                    # 尝试转换数值列
                    for col in df.columns:
                        try:
                            df[col] = pd.to_numeric(df[col])
                        except:
                            pass

                    # 显示DataFrame
                    st.dataframe(df, use_container_width=True)

                    # 显示基本统计
                    if len(df) > 0:
                        st.caption(f"📊 显示 {len(df)} 行数据")
                else:
                    st.text(output)
            else:
                st.text(output)

        except Exception:
            st.text(output)

    @staticmethod
    def _display_series_data(output, result):
        """显示序列数据"""
        st.subheader("📊 数据序列")

        try:
            lines = output.strip().split('\n')
            data_dict = {}

            # 解析序列数据 - 改进版本，正确处理DataFrame格式
            for i, line in enumerate(lines):
                line = line.strip()
                if line and not line.startswith('Name:') and not line.startswith('dtype:'):
                    # 跳过表头行
                    if i == 0 and any(header in line for header in ['产品名称', '地区', '类别', '销售员']):
                        continue

                    # 检查是否是DataFrame格式（索引 名称 数值）
                    parts = line.split()
                    if len(parts) >= 3 and parts[0].isdigit():
                        # DataFrame格式：索引 产品名称 销售额
                        try:
                            # 跳过索引，取产品名称和销售额
                            product_name = parts[1]
                            sales_amount = float(parts[2])
                            data_dict[product_name] = sales_amount
                        except (ValueError, IndexError):
                            continue
                    else:
                        # 传统序列格式：产品名称 销售额
                        parts = line.rsplit(None, 1)  # 从右边分割
                        if len(parts) == 2:
                            try:
                                key = parts[0]
                                value = float(parts[1])
                                data_dict[key] = value
                            except ValueError:
                                continue

            if data_dict:
                # 根据查询内容推断列名
                query = result.get('query', '').lower()

                # 智能推断列名
                if '产品' in query:
                    col1_name = '产品名称'
                elif '地区' in query:
                    col1_name = '地区'
                elif '类别' in query:
                    col1_name = '类别'
                elif '销售员' in query:
                    col1_name = '销售员'
                else:
                    col1_name = '项目'

                if '销售额' in query:
                    col2_name = '销售额'
                elif '销量' in query:
                    col2_name = '销量'
                elif '数量' in query:
                    col2_name = '数量'
                elif '金额' in query:
                    col2_name = '金额'
                else:
                    col2_name = '数值'

                # 创建DataFrame
                df = pd.DataFrame(list(data_dict.items()), columns=[col1_name, col2_name])

                # 按数值排序（降序）
                df = df.sort_values(col2_name, ascending=False)

                # 显示表格
                st.dataframe(df, use_container_width=True, hide_index=True)

                # 只有在没有生成图表时，才显示自动条形图
                has_chart = result.get('has_chart', False)
                uses_plotly_native = result.get('uses_plotly_native', False)
                # 如果使用了Plotly原生图表或有其他图表，都不显示自动柱状图
                if len(df) <= 15 and not has_chart and not uses_plotly_native:
                    st.subheader("📊 可视化")
                    try:
                        chart_data = df.set_index(col1_name)[col2_name]
                        # 确保数据是数值类型且没有无穷值
                        chart_data = pd.to_numeric(chart_data, errors='coerce').fillna(0)
                        if not chart_data.empty and chart_data.sum() != 0:
                            st.bar_chart(chart_data)
                        else:
                            st.info("数据无法生成图表")
                    except Exception as e:
                        st.warning(f"图表生成失败: {e}")
                        # 显示数据表格作为备用
                        pass

                # 显示统计信息
                col1_ui, col2_ui, col3_ui = st.columns(3)
                with col1_ui:
                    st.metric("📊 项目数量", len(df))
                with col2_ui:
                    st.metric(f"📊 {col2_name}总计", f"{df[col2_name].sum():,.2f}")
                with col3_ui:
                    st.metric(f"📊 {col2_name}平均", f"{df[col2_name].mean():,.2f}")
            else:
                st.text(output)

        except Exception:
            st.text(output)

    @staticmethod
    def _display_correlation_matrix(output, result):
        """显示相关性矩阵"""
        st.subheader("🔗 相关性矩阵")

        try:
            lines = output.strip().split('\n')

            # 找到矩阵数据
            matrix_lines = []
            headers = []

            for line in lines:
                line = line.strip()
                if line and any(char.isdigit() or char == '-' for char in line):
                    parts = line.split()
                    if len(parts) >= 2:
                        if not headers:
                            # 可能是第一行，包含列名
                            if parts[0] in ['价格', '销量', '库存', '评分', '销售额']:
                                headers = parts[1:]
                                matrix_lines.append([float(x) for x in parts[1:]])
                            else:
                                try:
                                    matrix_lines.append([float(x) for x in parts])
                                except:
                                    continue
                        else:
                            try:
                                matrix_lines.append([float(x) for x in parts[1:]])
                            except:
                                continue

            if matrix_lines and len(matrix_lines) >= 2:
                # 创建相关性矩阵DataFrame
                if not headers:
                    headers = [f'列{i+1}' for i in range(len(matrix_lines[0]))]

                index_names = [f'行{i+1}' for i in range(len(matrix_lines))]

                # 尝试从输出中提取真实的列名
                for line in lines:
                    if any(name in line for name in ['价格', '销量', '库存', '评分', '销售额']):
                        potential_names = re.findall(r'[\u4e00-\u9fff]+', line)
                        if len(potential_names) >= len(headers):
                            headers = potential_names[:len(headers)]
                            index_names = potential_names[:len(matrix_lines)]
                            break

                df_corr = pd.DataFrame(matrix_lines, columns=headers, index=index_names)

                # 显示相关性矩阵
                st.dataframe(df_corr.round(4), use_container_width=True)

                # 显示关键洞察
                st.subheader("💡 关键洞察")

                # 找出强相关关系
                strong_correlations = []
                for i in range(len(df_corr)):
                    for j in range(i+1, len(df_corr.columns)):
                        corr_value = df_corr.iloc[i, j]
                        if abs(corr_value) > 0.7:
                            strong_correlations.append({
                                'var1': df_corr.index[i],
                                'var2': df_corr.columns[j],
                                'correlation': corr_value
                            })

                if strong_correlations:
                    for corr in strong_correlations:
                        if corr['correlation'] > 0:
                            st.success(f"🔗 {corr['var1']} 与 {corr['var2']} 强正相关 ({corr['correlation']:.3f})")
                        else:
                            st.warning(f"🔗 {corr['var1']} 与 {corr['var2']} 强负相关 ({corr['correlation']:.3f})")
                else:
                    st.info("📊 未发现强相关关系 (|r| > 0.7)")
            else:
                st.text(output)

        except Exception:
            st.text(output)

    @staticmethod
    def _display_formatted_text(output, result):
        """显示格式化文本"""
        st.subheader("📄 分析结果")

        # 检查是否是空输出
        if not output.strip():
            st.info("ℹ️ 分析完成，但没有文本输出")
            return

        # 对于较长的文本，使用代码块显示
        if len(output) > 200:
            st.code(output, language=None)
        else:
            st.text(output)

        # 如果输出很短，可能是简单的确认消息
        if len(output.strip()) < 50:
            st.success("✅ 操作完成")

    @staticmethod
    def _display_user_chart(result):
        """显示用户生成的图表"""
        if not result.get('has_chart'):
            return

        st.subheader("📊 数据可视化")

        # 优先显示matplotlib图表对象
        if result.get('chart_figure'):
            try:
                # 使用Streamlit的pyplot显示，并优化样式
                import matplotlib.pyplot as plt

                # 设置Streamlit风格的图表样式
                plt.style.use('default')  # 使用默认样式，更接近Streamlit

                # 显示图表
                st.pyplot(result['chart_figure'], clear_figure=True, use_container_width=True)
                st.caption("✨ AI生成的数据可视化图表")
                return
            except Exception as e:
                st.warning(f"图表显示失败: {e}")

        # 备用方案：显示保存的图表文件
        if result.get('chart_path'):
            try:
                import os
                if os.path.exists(result['chart_path']):
                    # 使用容器宽度显示，保持一致性
                    st.image(result['chart_path'],
                            caption="✨ AI生成的数据可视化图表",
                            use_column_width=True)
                else:
                    st.warning("图表文件不存在")
            except Exception as e:
                st.warning(f"图表加载失败: {e}")

        # 最后备用方案：检查charts目录中的最新图表
        try:
            import os
            import glob
            charts_dir = 'charts'
            if os.path.exists(charts_dir):
                chart_files = glob.glob(os.path.join(charts_dir, '*.png'))
                if chart_files:
                    # 获取最新的图表文件
                    latest_chart = max(chart_files, key=os.path.getctime)
                    st.image(latest_chart,
                            caption="✨ AI生成的数据可视化图表",
                            use_column_width=True)
        except Exception:
            st.info("📊 图表已生成，但显示时出现问题")

    @staticmethod
    def _display_mixed_data_with_answer(output, result):
        """显示包含数据表格和答案的混合输出"""
        st.subheader("📊 数据分析结果")

        lines = output.strip().split('\n')
        data_lines = []
        answer_lines = []

        # 分离数据和答案
        for line in lines:
            line = line.strip()
            if line:
                # 检查是否是数据行
                if re.match(r'^\d+\s+\S+\s+[\d.-]+', line) or any(header in line for header in ['产品名称', '地区', '类别']):
                    data_lines.append(line)
                # 检查是否是答案行
                elif any(keyword in line for keyword in ['答案:', '最高', '最低', '最大', '最小']):
                    answer_lines.append(line)

        # 显示数据表格
        if data_lines:
            st.subheader("📊 详细数据")
            # 使用series_data的显示逻辑
            data_output = '\n'.join(data_lines)
            EnhancedResultFormatter._display_series_data(data_output, result)

        # 显示答案
        if answer_lines:
            st.subheader("🎯 分析结论")
            for answer in answer_lines:
                if '答案:' in answer:
                    st.success(f"💡 {answer}")
                else:
                    st.info(f"📋 {answer}")

        # 如果没有明确的答案，显示原始输出
        if not answer_lines and not data_lines:
            st.text(output)

# 保持向后兼容性的别名
ResultFormatter = EnhancedResultFormatter
