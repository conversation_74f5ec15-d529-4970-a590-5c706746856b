#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元数据管理模块
为PandasAI应用提供表格列名解释和元数据管理功能
帮助大语言模型更好地理解数据结构和业务含义
"""

import json
import yaml
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging
from dataclasses import dataclass, asdict
import re

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ColumnMetadata:
    """列元数据类"""
    name: str                    # 列名
    display_name: str           # 显示名称
    description: str            # 详细描述
    data_type: str             # 数据类型
    business_meaning: str      # 业务含义
    examples: List[str]        # 示例值
    constraints: Dict[str, Any] # 约束条件
    tags: List[str]            # 标签
    created_at: str            # 创建时间
    updated_at: str            # 更新时间

    def __post_init__(self):
        """初始化后处理"""
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()

@dataclass
class TableMetadata:
    """表格元数据类"""
    table_name: str                      # 表格名称
    description: str                     # 表格描述
    business_domain: str                 # 业务领域
    columns: Dict[str, ColumnMetadata]   # 列元数据
    relationships: Dict[str, str]        # 列间关系
    primary_keys: List[str]             # 主键列
    created_at: str                     # 创建时间
    updated_at: str                     # 更新时间
    version: str                        # 版本号

    def __post_init__(self):
        """初始化后处理"""
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()
        if not self.version:
            self.version = "1.0.0"

class MetadataManager:
    """元数据管理器"""
    
    def __init__(self, config_dir: Union[str, Path] = "metadata_config"):
        """
        初始化元数据管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self.tables_config_file = self.config_dir / "tables_metadata.json"
        self.templates_config_file = self.config_dir / "column_templates.json"
        
        # 加载配置
        self.tables_metadata: Dict[str, TableMetadata] = {}
        self.column_templates: Dict[str, Dict] = {}
        
        self._load_configurations()
        self._initialize_default_templates()
    
    def _load_configurations(self):
        """加载配置文件"""
        try:
            # 加载表格元数据
            if self.tables_config_file.exists():
                with open(self.tables_config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for table_name, table_data in data.items():
                        # 转换列元数据
                        columns = {}
                        for col_name, col_data in table_data.get('columns', {}).items():
                            columns[col_name] = ColumnMetadata(**col_data)
                        
                        table_data['columns'] = columns
                        self.tables_metadata[table_name] = TableMetadata(**table_data)
            
            # 加载列模板
            if self.templates_config_file.exists():
                with open(self.templates_config_file, 'r', encoding='utf-8') as f:
                    self.column_templates = json.load(f)
                    
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
    
    def _save_configurations(self):
        """保存配置文件"""
        try:
            # 保存表格元数据
            tables_data = {}
            for table_name, table_metadata in self.tables_metadata.items():
                table_dict = asdict(table_metadata)
                # 转换列元数据为字典
                columns_dict = {}
                for col_name, col_metadata in table_metadata.columns.items():
                    columns_dict[col_name] = asdict(col_metadata)
                table_dict['columns'] = columns_dict
                tables_data[table_name] = table_dict
            
            with open(self.tables_config_file, 'w', encoding='utf-8') as f:
                json.dump(tables_data, f, ensure_ascii=False, indent=2)
            
            # 保存列模板
            with open(self.templates_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.column_templates, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def _initialize_default_templates(self):
        """初始化默认列模板"""
        if not self.column_templates:
            self.column_templates = {
                "销售相关": {
                    "销售额": {
                        "description": "产品或服务的销售金额",
                        "business_meaning": "反映业务收入情况的核心指标",
                        "data_type": "float",
                        "constraints": {"min": 0},
                        "tags": ["财务", "收入", "KPI"]
                    },
                    "销量": {
                        "description": "产品销售的数量",
                        "business_meaning": "反映产品市场接受度和需求量",
                        "data_type": "int",
                        "constraints": {"min": 0},
                        "tags": ["销售", "数量", "市场"]
                    },
                    "销售员": {
                        "description": "负责销售的员工姓名",
                        "business_meaning": "用于分析个人销售业绩和团队管理",
                        "data_type": "string",
                        "tags": ["人员", "业绩", "管理"]
                    }
                },
                "产品相关": {
                    "产品名称": {
                        "description": "产品的具体名称或型号",
                        "business_meaning": "用于产品分析和库存管理的标识",
                        "data_type": "string",
                        "tags": ["产品", "标识", "分类"]
                    },
                    "价格": {
                        "description": "产品的单价或售价",
                        "business_meaning": "定价策略和利润分析的基础数据",
                        "data_type": "float",
                        "constraints": {"min": 0},
                        "tags": ["定价", "财务", "策略"]
                    },
                    "库存": {
                        "description": "产品的库存数量",
                        "business_meaning": "库存管理和供应链优化的关键指标",
                        "data_type": "int",
                        "constraints": {"min": 0},
                        "tags": ["库存", "供应链", "管理"]
                    }
                },
                "地理相关": {
                    "地区": {
                        "description": "销售或业务发生的地理区域",
                        "business_meaning": "用于区域分析和市场策略制定",
                        "data_type": "string",
                        "tags": ["地理", "区域", "市场"]
                    },
                    "城市": {
                        "description": "具体的城市名称",
                        "business_meaning": "城市级别的市场分析和布局",
                        "data_type": "string",
                        "tags": ["地理", "城市", "市场"]
                    }
                },
                "时间相关": {
                    "日期": {
                        "description": "事件发生的具体日期",
                        "business_meaning": "时间序列分析和趋势预测的基础",
                        "data_type": "datetime",
                        "tags": ["时间", "趋势", "分析"]
                    },
                    "月份": {
                        "description": "事件发生的月份",
                        "business_meaning": "月度业绩分析和季节性趋势识别",
                        "data_type": "string",
                        "tags": ["时间", "月度", "季节性"]
                    }
                }
            }
            self._save_configurations()
    
    def register_table(self, table_name: str, df: pd.DataFrame,
                      description: str = "", business_domain: str = "",
                      use_smart_inference: bool = True) -> TableMetadata:
        """
        注册新表格并生成元数据

        Args:
            table_name: 表格名称
            df: DataFrame对象
            description: 表格描述
            business_domain: 业务领域
            use_smart_inference: 是否使用智能推断

        Returns:
            TableMetadata: 生成的表格元数据
        """
        logger.info(f"注册表格: {table_name}")

        if use_smart_inference:
            try:
                from metadata_inference import metadata_inference

                # 使用智能推断
                inferred_metadata = metadata_inference.infer_table_metadata(table_name, df)

                # 创建列元数据对象
                columns_metadata = {}
                for col_name, col_data in inferred_metadata["columns"].items():
                    columns_metadata[col_name] = ColumnMetadata(
                        name=col_data["name"],
                        display_name=col_data["display_name"],
                        description=col_data["description"],
                        data_type=col_data["data_type"],
                        business_meaning=col_data["business_meaning"],
                        examples=col_data["examples"],
                        constraints=col_data["constraints"],
                        tags=col_data["tags"],
                        created_at="",
                        updated_at=""
                    )

                # 创建表格元数据
                table_metadata = TableMetadata(
                    table_name=table_name,
                    description=description or inferred_metadata["description"],
                    business_domain=business_domain or inferred_metadata["business_domain"],
                    columns=columns_metadata,
                    relationships=inferred_metadata["relationships"],
                    primary_keys=inferred_metadata["primary_keys"],
                    created_at="",
                    updated_at="",
                    version=""
                )

                logger.info(f"使用智能推断注册表格 {table_name}，置信度: {inferred_metadata['inference_info']['confidence_score']:.2f}")

            except ImportError:
                logger.warning("智能推断模块不可用，使用基础推断")
                use_smart_inference = False

        if not use_smart_inference:
            # 使用基础推断
            columns_metadata = {}
            for col_name in df.columns:
                col_metadata = self._infer_column_metadata(col_name, df[col_name])
                columns_metadata[col_name] = col_metadata

            # 创建表格元数据
            table_metadata = TableMetadata(
                table_name=table_name,
                description=description or f"自动生成的{table_name}表格元数据",
                business_domain=business_domain or "通用",
                columns=columns_metadata,
                relationships={},
                primary_keys=[],
                created_at="",
                updated_at="",
                version=""
            )

        # 保存到内存和文件
        self.tables_metadata[table_name] = table_metadata
        self._save_configurations()

        logger.info(f"表格 {table_name} 注册成功，包含 {len(table_metadata.columns)} 列")
        return table_metadata

    def _infer_column_metadata(self, col_name: str, series: pd.Series) -> ColumnMetadata:
        """
        推断列元数据

        Args:
            col_name: 列名
            series: 列数据

        Returns:
            ColumnMetadata: 推断的列元数据
        """
        # 基本信息
        data_type = str(series.dtype)
        examples = []

        # 获取示例值（非空且唯一）
        non_null_values = series.dropna().unique()
        if len(non_null_values) > 0:
            examples = [str(val) for val in non_null_values[:3]]

        # 从模板匹配推断
        template_match = self._match_column_template(col_name, data_type)

        if template_match:
            return ColumnMetadata(
                name=col_name,
                display_name=template_match.get('display_name', col_name),
                description=template_match.get('description', f"{col_name}列"),
                data_type=data_type,
                business_meaning=template_match.get('business_meaning', "待定义"),
                examples=examples,
                constraints=template_match.get('constraints', {}),
                tags=template_match.get('tags', []),
                created_at="",
                updated_at=""
            )
        else:
            # 默认推断
            return ColumnMetadata(
                name=col_name,
                display_name=col_name,
                description=f"{col_name}列的数据",
                data_type=data_type,
                business_meaning="需要进一步定义业务含义",
                examples=examples,
                constraints={},
                tags=["未分类"],
                created_at="",
                updated_at=""
            )

    def _match_column_template(self, col_name: str, data_type: str) -> Optional[Dict]:
        """
        匹配列模板

        Args:
            col_name: 列名
            data_type: 数据类型

        Returns:
            匹配的模板字典或None
        """
        col_name_lower = col_name.lower()

        # 遍历所有模板类别
        for templates in self.column_templates.values():
            for template_name, template_config in templates.items():
                # 精确匹配
                if col_name == template_name:
                    return template_config

                # 包含匹配
                if template_name in col_name or col_name in template_name:
                    return template_config

                # 关键词匹配
                keywords = template_config.get('keywords', [template_name])
                for keyword in keywords:
                    if keyword.lower() in col_name_lower:
                        return template_config

        return None

    def get_table_metadata(self, table_name: str) -> Optional[TableMetadata]:
        """获取表格元数据"""
        return self.tables_metadata.get(table_name)

    def get_column_metadata(self, table_name: str, column_name: str) -> Optional[ColumnMetadata]:
        """获取列元数据"""
        table_metadata = self.get_table_metadata(table_name)
        if table_metadata:
            return table_metadata.columns.get(column_name)
        return None

    def update_column_metadata(self, table_name: str, column_name: str,
                             updates: Dict[str, Any]) -> bool:
        """
        更新列元数据

        Args:
            table_name: 表格名称
            column_name: 列名
            updates: 更新的字段

        Returns:
            bool: 更新是否成功
        """
        try:
            table_metadata = self.get_table_metadata(table_name)
            if not table_metadata:
                logger.error(f"表格 {table_name} 不存在")
                return False

            column_metadata = table_metadata.columns.get(column_name)
            if not column_metadata:
                logger.error(f"列 {column_name} 不存在于表格 {table_name}")
                return False

            # 更新字段
            for field, value in updates.items():
                if hasattr(column_metadata, field):
                    setattr(column_metadata, field, value)

            # 更新时间戳
            column_metadata.updated_at = datetime.now().isoformat()
            table_metadata.updated_at = datetime.now().isoformat()

            # 保存配置
            self._save_configurations()

            logger.info(f"列 {column_name} 元数据更新成功")
            return True

        except Exception as e:
            logger.error(f"更新列元数据失败: {e}")
            return False

    def generate_llm_context(self, table_name: str, df: pd.DataFrame) -> str:
        """
        为LLM生成表格上下文信息

        Args:
            table_name: 表格名称
            df: DataFrame对象

        Returns:
            str: 格式化的上下文信息
        """
        table_metadata = self.get_table_metadata(table_name)

        if not table_metadata:
            # 如果没有元数据，自动注册
            table_metadata = self.register_table(table_name, df)

        context_parts = []

        # 表格基本信息
        context_parts.append(f"表格名称: {table_metadata.table_name}")
        context_parts.append(f"业务领域: {table_metadata.business_domain}")
        context_parts.append(f"表格描述: {table_metadata.description}")
        context_parts.append("")

        # 列信息详解
        context_parts.append("列信息详解:")
        for col_name, col_metadata in table_metadata.columns.items():
            col_info = []
            col_info.append(f"- {col_name} ({col_metadata.display_name})")
            col_info.append(f"  描述: {col_metadata.description}")
            col_info.append(f"  业务含义: {col_metadata.business_meaning}")
            col_info.append(f"  数据类型: {col_metadata.data_type}")

            if col_metadata.examples:
                col_info.append(f"  示例值: {', '.join(col_metadata.examples[:3])}")

            if col_metadata.constraints:
                constraints_str = ', '.join([f"{k}={v}" for k, v in col_metadata.constraints.items()])
                col_info.append(f"  约束条件: {constraints_str}")

            if col_metadata.tags:
                col_info.append(f"  标签: {', '.join(col_metadata.tags)}")

            context_parts.extend(col_info)
            context_parts.append("")

        # 列间关系
        if table_metadata.relationships:
            context_parts.append("列间关系:")
            for rel_key, rel_desc in table_metadata.relationships.items():
                context_parts.append(f"- {rel_key}: {rel_desc}")
            context_parts.append("")

        # 主键信息
        if table_metadata.primary_keys:
            context_parts.append(f"主键列: {', '.join(table_metadata.primary_keys)}")
            context_parts.append("")

        return "\n".join(context_parts)

    def get_all_tables(self) -> List[str]:
        """获取所有已注册的表格名称"""
        return list(self.tables_metadata.keys())

    def export_metadata(self, output_file: Union[str, Path], format: str = "json") -> bool:
        """
        导出元数据配置

        Args:
            output_file: 输出文件路径
            format: 导出格式 (json/yaml)

        Returns:
            bool: 导出是否成功
        """
        try:
            output_path = Path(output_file)

            # 准备导出数据
            export_data = {
                "tables": {},
                "templates": self.column_templates,
                "export_time": datetime.now().isoformat(),
                "version": "1.0.0"
            }

            # 转换表格元数据
            for table_name, table_metadata in self.tables_metadata.items():
                table_dict = asdict(table_metadata)
                columns_dict = {}
                for col_name, col_metadata in table_metadata.columns.items():
                    columns_dict[col_name] = asdict(col_metadata)
                table_dict['columns'] = columns_dict
                export_data["tables"][table_name] = table_dict

            # 根据格式保存
            if format.lower() == "yaml":
                with open(output_path, 'w', encoding='utf-8') as f:
                    yaml.dump(export_data, f, default_flow_style=False, allow_unicode=True)
            else:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

            logger.info(f"元数据导出成功: {output_path}")
            return True

        except Exception as e:
            logger.error(f"导出元数据失败: {e}")
            return False

    def import_metadata(self, input_file: Union[str, Path]) -> bool:
        """
        导入元数据配置

        Args:
            input_file: 输入文件路径

        Returns:
            bool: 导入是否成功
        """
        try:
            input_path = Path(input_file)

            if not input_path.exists():
                logger.error(f"文件不存在: {input_path}")
                return False

            # 根据文件扩展名选择解析方式
            if input_path.suffix.lower() in ['.yaml', '.yml']:
                with open(input_path, 'r', encoding='utf-8') as f:
                    import_data = yaml.safe_load(f)
            else:
                with open(input_path, 'r', encoding='utf-8') as f:
                    import_data = json.load(f)

            # 导入模板
            if 'templates' in import_data:
                self.column_templates.update(import_data['templates'])

            # 导入表格元数据
            if 'tables' in import_data:
                for table_name, table_data in import_data['tables'].items():
                    # 转换列元数据
                    columns = {}
                    for col_name, col_data in table_data.get('columns', {}).items():
                        columns[col_name] = ColumnMetadata(**col_data)

                    table_data['columns'] = columns
                    self.tables_metadata[table_name] = TableMetadata(**table_data)

            # 保存配置
            self._save_configurations()

            logger.info(f"元数据导入成功: {input_path}")
            return True

        except Exception as e:
            logger.error(f"导入元数据失败: {e}")
            return False

    def add_column_template(self, category: str, template_name: str,
                           template_config: Dict[str, Any]) -> bool:
        """
        添加列模板

        Args:
            category: 模板类别
            template_name: 模板名称
            template_config: 模板配置

        Returns:
            bool: 添加是否成功
        """
        try:
            if category not in self.column_templates:
                self.column_templates[category] = {}

            self.column_templates[category][template_name] = template_config
            self._save_configurations()

            logger.info(f"添加列模板成功: {category}.{template_name}")
            return True

        except Exception as e:
            logger.error(f"添加列模板失败: {e}")
            return False

    def get_column_suggestions(self, col_name: str) -> List[Dict[str, Any]]:
        """
        获取列名建议

        Args:
            col_name: 列名

        Returns:
            List[Dict]: 建议列表
        """
        suggestions = []
        col_name_lower = col_name.lower()

        # 遍历所有模板寻找匹配
        for category, templates in self.column_templates.items():
            for template_name, template_config in templates.items():
                score = 0

                # 精确匹配
                if col_name == template_name:
                    score = 100
                # 包含匹配
                elif template_name in col_name or col_name in template_name:
                    score = 80
                # 关键词匹配
                else:
                    keywords = template_config.get('keywords', [template_name])
                    for keyword in keywords:
                        if keyword.lower() in col_name_lower:
                            score = max(score, 60)

                if score > 0:
                    suggestion = {
                        'category': category,
                        'template_name': template_name,
                        'score': score,
                        'config': template_config
                    }
                    suggestions.append(suggestion)

        # 按分数排序
        suggestions.sort(key=lambda x: x['score'], reverse=True)
        return suggestions[:5]  # 返回前5个建议

    def validate_metadata(self, table_name: str) -> Dict[str, List[str]]:
        """
        验证表格元数据的完整性

        Args:
            table_name: 表格名称

        Returns:
            Dict: 验证结果，包含错误和警告
        """
        result = {
            'errors': [],
            'warnings': [],
            'suggestions': []
        }

        table_metadata = self.get_table_metadata(table_name)
        if not table_metadata:
            result['errors'].append(f"表格 {table_name} 不存在")
            return result

        # 检查表格基本信息
        if not table_metadata.description or table_metadata.description.startswith("自动生成"):
            result['warnings'].append("表格描述需要完善")

        if not table_metadata.business_domain or table_metadata.business_domain == "通用":
            result['warnings'].append("建议指定具体的业务领域")

        # 检查列元数据
        for col_name, col_metadata in table_metadata.columns.items():
            if col_metadata.business_meaning == "需要进一步定义业务含义":
                result['warnings'].append(f"列 {col_name} 的业务含义需要定义")

            if not col_metadata.examples:
                result['suggestions'].append(f"建议为列 {col_name} 添加示例值")

            if col_metadata.tags == ["未分类"]:
                result['suggestions'].append(f"建议为列 {col_name} 添加合适的标签")

        return result

    def cleanup_test_tables(self) -> Dict[str, int]:
        """清理测试表格，只保留用户真正上传的表格"""
        # 识别测试表格的模式
        test_patterns = [
            'test', 'demo', 'sample', 'example', 'temp', 'tmp',
            'sales_data', 'finance_data', 'inventory_data',
            'metadata_test', 'ui_test', 'save_test', 'batch_test',
            'customer_sales', 'product_inventory', 'meaningful_table',
            'cryptic_table', 'customer_info_single_table', 'sales_metadata_test',
            'customer_data', 'product_data', 'order_data', 'sales_with_relationships'
        ]

        tables_to_delete = []

        for table_name in list(self.tables_metadata.keys()):
            # 检查是否匹配测试模式
            is_test_table = any(pattern in table_name.lower() for pattern in test_patterns)

            if is_test_table:
                tables_to_delete.append(table_name)

        # 执行删除
        deleted_count = 0
        for table_name in tables_to_delete:
            try:
                del self.tables_metadata[table_name]
                deleted_count += 1
                logger.info(f"已删除测试表格: {table_name}")
            except Exception as e:
                logger.error(f"删除表格 {table_name} 失败: {e}")

        # 保存清理后的元数据
        if deleted_count > 0:
            self._save_configurations()

        return {
            'deleted': deleted_count,
            'remaining': len(self.tables_metadata),
            'deleted_tables': tables_to_delete
        }

# 创建全局实例
metadata_manager = MetadataManager()
