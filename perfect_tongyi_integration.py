#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问完美集成示例 - 最终版本
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai.llm.base import LLM
import requests
import re

load_dotenv()

class TongyiQianwenLLM(LLM):
    def __init__(self, model="qwen-plus"):
        self.api_key = os.getenv('DASHSCOPE_API_KEY')
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        
    def call(self, instruction, value):
        prompt = f"""你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
{value}

用户指令: {instruction}

要求:
1. 只返回可执行的Python代码
2. 使用变量名df表示DataFrame
3. 不要包含创建DataFrame的代码
4. 不要包含任何中文注释或解释
5. 使用print()输出最终结果，特别注意：
   - 对于"哪个/哪种...最高/最低"类型查询，必须输出：
     a) 所有项目的统计数据：print(grouped_data)
     b) 明确的答案：print(f"答案: 具体项目名称, 具体指标: 具体数值")
   - 对于比较分析，必须输出完整数据和结论
   - 不能只计算结果而不输出，每个分析都必须有print语句
6. 如果需要生成图表，请遵循以下规则：
   - 导入: import matplotlib.pyplot as plt
   - 设置图表大小: plt.figure(figsize=(12, 8))
   - 必须包含标题: plt.title('图表标题', fontsize=16, fontweight='bold')
   - 必须包含轴标签: plt.xlabel('X轴标签', fontsize=12), plt.ylabel('Y轴标签', fontsize=12)
   - 如果X轴标签较长，使用: plt.xticks(rotation=45, ha='right')
   - 如果有多个数据系列，必须包含图例: plt.legend(fontsize=10)
   - 添加网格线: plt.grid(True, alpha=0.3)
   - 绘图后使用: plt.tight_layout() 然后 save_chart()
   - 不要使用 plt.show() 或 plt.close()
   - 图表会自动在前端显示
   - 示例代码结构:
     ```python
     plt.figure(figsize=(12, 8))
     plt.bar(x_data, y_data, label='数据标签', alpha=0.8)
     plt.title('图表标题', fontsize=16, fontweight='bold')
     plt.xlabel('X轴标签', fontsize=12)
     plt.ylabel('Y轴标签', fontsize=12)
     plt.xticks(rotation=45, ha='right')
     plt.legend(fontsize=10)
     plt.grid(True, alpha=0.3)
     plt.tight_layout()
     save_chart()
     ```

代码:"""
        
        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        data = {"model": self.model, "messages": [{"role": "user", "content": prompt}], "temperature": 0.1, "max_tokens": 500}
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                code = response.json()['choices'][0]['message']['content']
                
                # 清理代码
                code = self.clean_code(code)
                return code
            else:
                return "print('API调用失败')"
        except Exception as e:
            return f"print('API异常: {e}')"
    
    def clean_code(self, code):
        """清理生成的代码"""
        # 移除markdown标记
        code = re.sub(r'```python\n?', '', code)
        code = re.sub(r'```\n?', '', code)
        
        # 按行处理
        lines = code.split('\n')
        clean_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 跳过中文解释行
            if self.contains_chinese_explanation(line):
                continue
                
            # 跳过注释行（但保留代码中的英文注释）
            if line.startswith('#') and any(ord(c) > 127 for c in line):
                continue
                
            clean_lines.append(line)
        
        return '\n'.join(clean_lines).strip()
    
    def contains_chinese_explanation(self, line):
        """检查是否包含中文解释"""
        # 检查是否包含中文标点符号
        chinese_punctuation = '。，：；！？""''（）【】'
        if any(p in line for p in chinese_punctuation):
            return True
            
        # 检查是否以中文开头且不是代码
        if line and ord(line[0]) > 127 and not any(op in line for op in ['=', '(', '[', 'df', 'print']):
            return True
            
        return False
    
    @property
    def type(self):
        return "tongyi_qianwen"

def analyze_data(df, query, table_name="data_table", use_metadata=True):
    """分析数据并返回结果，支持元数据增强"""
    import io
    import sys
    from contextlib import redirect_stdout, redirect_stderr

    # 创建结果字典
    result = {
        'query': query,
        'table_name': table_name,
        'data_shape': df.shape,
        'code': '',
        'output': '',
        'error': None,
        'success': False,
        'chart_path': None,
        'chart_figure': None,
        'has_chart': False,
        'metadata_used': use_metadata
    }

    print(f"🔍 查询: {query}")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 表格名称: {table_name}")

    try:
        # 根据是否使用元数据选择LLM
        if use_metadata:
            try:
                from enhanced_tongyi_integration import EnhancedTongyiQianwenLLM
                llm = EnhancedTongyiQianwenLLM()
                llm.set_current_data(table_name, df)
                print("🎯 使用增强版LLM（包含元数据支持）")
            except ImportError:
                print("⚠️ 增强版LLM不可用，使用标准版本")
                llm = TongyiQianwenLLM()
        else:
            llm = TongyiQianwenLLM()
            print("📝 使用标准版LLM")

        code = llm.call(query, df.to_string())
        result['code'] = code

        print(f"📝 生成的代码:")
        print(code)
        print(f"🚀 执行结果:")

        # 捕获执行输出
        output_buffer = io.StringIO()
        error_buffer = io.StringIO()

        # 创建执行环境，包含df变量和图表保存功能
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        import matplotlib.pyplot as plt
        from datetime import datetime
        import os
        import warnings

        # 立即抑制所有matplotlib警告
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
        warnings.filterwarnings('ignore', message='.*Glyph.*missing from current font.*')
        warnings.filterwarnings('ignore', message='.*UserWarning.*')
        warnings.filterwarnings('ignore')

        # 设置中文字体支持 - 使用更彻底的方法
        def setup_chinese_font():
            """设置中文字体支持"""
            try:
                import matplotlib.font_manager as fm
                import warnings

                # 抑制字体警告
                warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
                warnings.filterwarnings('ignore', message='.*Glyph.*missing from current font.*')
                warnings.filterwarnings('ignore', message='.*UserWarning.*')

                # 尝试设置中文字体
                try:
                    # 方法1: 尝试系统字体
                    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
                    plt.rcParams['axes.unicode_minus'] = False

                    # 测试字体是否可用
                    fig, ax = plt.subplots(figsize=(1, 1))
                    ax.text(0.5, 0.5, '测试', fontsize=12)
                    plt.close(fig)

                    print(f"✅ 中文字体设置成功")

                except:
                    # 方法2: 如果系统字体不可用，使用英文标签
                    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                    plt.rcParams['axes.unicode_minus'] = False
                    print("⚠️ 中文字体不可用，将使用英文标签")

                    # 设置标志，后续使用英文标签
                    return False

            except Exception as e:
                print(f"⚠️ 字体设置失败: {e}")
                plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
                return False

            return True

        # 调用字体设置并获取结果
        chinese_font_available = setup_chinese_font()

        # 确保charts目录存在
        charts_dir = 'charts'
        os.makedirs(charts_dir, exist_ok=True)

        # 生成唯一的图表文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f"chart_{timestamp}.png"
        chart_path = os.path.join(charts_dir, chart_filename)

        # 自定义的图表保存函数
        def save_and_show_chart():
            """保存图表并返回图表对象"""
            try:
                plt.tight_layout()
                # 获取当前图表对象
                current_fig = plt.gcf()
                # 保存图表到文件（可选）
                plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                print(f"📊 图表已生成")
                # 返回图表对象而不是关闭它
                return current_fig
            except Exception as e:
                print(f"❌ 图表生成失败: {e}")
                return None

        # 用于存储图表对象的变量
        chart_figure = None

        def save_chart_wrapper():
            nonlocal chart_figure
            chart_figure = save_and_show_chart()
            return chart_figure

        exec_globals = {
            'df': df,
            'pd': __import__('pandas'),
            'np': __import__('numpy'),
            'plt': plt,
            'matplotlib': matplotlib,
            'chart_path': chart_path,
            'save_chart': save_chart_wrapper,
            'print': lambda *args, **kwargs: print(*args, **kwargs, file=output_buffer)
        }

        with redirect_stdout(output_buffer), redirect_stderr(error_buffer):
            exec(code, exec_globals)

        # 获取输出
        output = output_buffer.getvalue()
        error_output = error_buffer.getvalue()

        if error_output:
            result['error'] = error_output
            print(f"❌ 执行警告: {error_output}")

        if output:
            result['output'] = output
            print(output)

        # 检查是否生成了图表
        if chart_figure is not None:
            result['chart_figure'] = chart_figure
            result['has_chart'] = True
            print(f"📊 图表已生成（matplotlib对象）")
        elif os.path.exists(chart_path):
            result['chart_path'] = chart_path
            result['has_chart'] = True
            print(f"📊 图表已生成: {chart_path}")

        result['success'] = True
        print("✅ 执行成功")

        # 显示元数据使用情况
        if result.get('metadata_used'):
            print("🎯 已使用元数据增强分析准确性")

    except Exception as e:
        result['error'] = str(e)
        result['success'] = False
        print(f"❌ 执行失败: {e}")

    print("-" * 50)
    return result

def demo_comprehensive_analysis():
    """综合演示"""
    print("🎯 通义千问 + PandasAI 综合演示")
    print("=" * 50)
    
    # 创建综合数据集
    data = {
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch', 'Mac Mini'],
        '类别': ['手机', '平板', '笔记本', '配件', '配件', '台式机'],
        '价格': [6999, 4599, 14999, 1899, 3199, 4999],
        '销量': [1200, 800, 400, 1500, 1000, 300],
        '库存': [150, 200, 80, 300, 250, 100],
        '评分': [4.8, 4.6, 4.9, 4.7, 4.5, 4.4],
        '上市月份': ['2023-09', '2023-10', '2023-11', '2023-09', '2023-10', '2023-11']
    }
    
    df = pd.DataFrame(data)
    print("📊 数据集概览:")
    print(df)
    print()
    
    # 执行各种分析
    queries = [
        "计算总销售额（价格×销量）",
        "找出销量最高的产品名称",
        "计算每个类别的平均价格",
        "找出评分最高的产品",
        "计算库存总价值（价格×库存）",
        "显示价格超过5000的产品",
        "按销量降序排列显示前3名产品",
        "计算配件类产品的总销量"
    ]
    
    for query in queries:
        analyze_data(df, query)

def test_chinese_data():
    """测试中文数据处理"""
    print("\n🇨🇳 中文数据处理测试")
    print("=" * 50)
    
    # 中文数据集
    data = {
        '城市': ['北京', '上海', '广州', '深圳', '杭州', '成都'],
        '人口万人': [2154, 2424, 1530, 1756, 1220, 1658],
        'GDP万亿': [4.03, 4.32, 2.82, 3.24, 1.81, 2.08],
        '房价元平米': [65000, 70000, 45000, 55000, 35000, 25000],
        '地区': ['华北', '华东', '华南', '华南', '华东', '西南']
    }
    
    df = pd.DataFrame(data)
    print("📊 中文数据集:")
    print(df)
    print()
    
    chinese_queries = [
        "哪个城市的GDP最高？",
        "人口超过2000万的城市有哪些？",
        "计算华东地区的平均房价",
        "按人口排序显示所有城市",
        "找出房价最便宜的城市",
        "计算所有城市的平均GDP"
    ]
    
    for query in chinese_queries:
        analyze_data(df, query)

def main():
    """主函数"""
    print("🎉 通义千问完美集成演示")
    print("=" * 60)
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        print("请在.env文件中配置: DASHSCOPE_API_KEY=your-api-key")
        return
    
    print(f"✅ API密钥已配置: {api_key[:10]}...{api_key[-4:]}")
    print()
    
    try:
        # 综合演示
        demo_comprehensive_analysis()
        
        # 中文数据测试
        test_chinese_data()
        
        print("\n" + "=" * 60)
        print("🎊 演示完成!")
        print("\n✅ 集成特点:")
        print("- 完美支持中文自然语言查询")
        print("- 生成准确可执行的Python代码")
        print("- 支持复杂数据分析任务")
        print("- 强大的代码清理和错误处理")
        print("- 适用于各种数据类型和查询")
        
        print("\n🚀 现在您可以:")
        print("1. 使用中文进行数据查询")
        print("2. 进行复杂的数据分析")
        print("3. 处理各种类型的数据集")
        print("4. 获得准确的分析结果")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
