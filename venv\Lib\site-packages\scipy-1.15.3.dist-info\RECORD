scipy-1.15.3-cp311-cp311-win_amd64.whl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.15.3.dist-info/DELVEWHEEL,sha256=9_1-7ERz51hIdmYJntakCBpgL_HnabTx1ssGf1jZK9c,577
scipy-1.15.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
scipy-1.15.3.dist-info/LICENSE.txt,sha256=8lDnzvFfvZnoaACT0RLXqnV5U1uZmVJo32yJrJJutgc,46763
scipy-1.15.3.dist-info/METADATA,sha256=GRYQOB8GGQ8R7Cxc1G8aeDNmXYOG0_6KcQwH72Ws39Q,60764
scipy-1.15.3.dist-info/RECORD,,
scipy-1.15.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy-1.15.3.dist-info/WHEEL,sha256=JdLTWhc73oJ-lqTBYGgiVontr_vhzwzbpAOin_2bxTI,85
scipy.libs/libscipy_openblas-f07f5a5d207a3a47104dca54d6d0c86a.dll,sha256=ayED8q5NhUeZi10YjpgB-6bLEkBK6OS__zGejMGUkAA,20155392
scipy/__config__.py,sha256=1pqqIrxAruTcqGneJ2ReNpqG2-Nt2oD3Fjy-XIV2Vh4,5646
scipy/__init__.py,sha256=aNeJ0O6ikSxAy3URm1BXmqwHqzCBT1UcirJIdrs9cko,4614
scipy/__pycache__/__config__.cpython-311.pyc,,
scipy/__pycache__/__init__.cpython-311.pyc,,
scipy/__pycache__/_distributor_init.cpython-311.pyc,,
scipy/__pycache__/conftest.cpython-311.pyc,,
scipy/__pycache__/version.cpython-311.pyc,,
scipy/_distributor_init.py,sha256=CmoJiFF1KyM0MvnPov6kBTO60D43AVlYsfYSmaSuoZY,629
scipy/_lib/__init__.py,sha256=lcHbxe9IkuoUZjXA8VX6-SYYCREBLq1cqoPo2mR6V1w,367
scipy/_lib/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/__pycache__/_array_api.cpython-311.pyc,,
scipy/_lib/__pycache__/_array_api_no_0d.cpython-311.pyc,,
scipy/_lib/__pycache__/_bunch.cpython-311.pyc,,
scipy/_lib/__pycache__/_ccallback.cpython-311.pyc,,
scipy/_lib/__pycache__/_disjoint_set.cpython-311.pyc,,
scipy/_lib/__pycache__/_docscrape.cpython-311.pyc,,
scipy/_lib/__pycache__/_elementwise_iterative_method.cpython-311.pyc,,
scipy/_lib/__pycache__/_finite_differences.cpython-311.pyc,,
scipy/_lib/__pycache__/_gcutils.cpython-311.pyc,,
scipy/_lib/__pycache__/_pep440.cpython-311.pyc,,
scipy/_lib/__pycache__/_testutils.cpython-311.pyc,,
scipy/_lib/__pycache__/_threadsafety.cpython-311.pyc,,
scipy/_lib/__pycache__/_tmpdirs.cpython-311.pyc,,
scipy/_lib/__pycache__/_util.cpython-311.pyc,,
scipy/_lib/__pycache__/decorator.cpython-311.pyc,,
scipy/_lib/__pycache__/deprecation.cpython-311.pyc,,
scipy/_lib/__pycache__/doccer.cpython-311.pyc,,
scipy/_lib/__pycache__/uarray.cpython-311.pyc,,
scipy/_lib/_array_api.py,sha256=QrpOcXNe1l-MVd8Ih-6AXLke-1Gwbojf_9UJPrZ8AFI,22959
scipy/_lib/_array_api_no_0d.py,sha256=00uQyWlMxKpGlHGZkGzNmbwYGG2vrWjPV_O3j2YExC8,4556
scipy/_lib/_bunch.py,sha256=wQK9j5N61P1eXt73dEZnUcEc8bS5Y59qOgp_av6eGPc,8345
scipy/_lib/_ccallback.py,sha256=RDz5WUY_jgPtUlEgtm-VGxcub9nvF4laqIsyExK8Nzk,7338
scipy/_lib/_ccallback_c.cp311-win_amd64.dll.a,sha256=BlvEdTYS1QYE2xd_-HhdCIMWaLDzR-_lkL_QQqhmG8Y,1608
scipy/_lib/_ccallback_c.cp311-win_amd64.pyd,sha256=s0KXsfhpgK9kjiOcYtwzsOkgzFTaPwZw18PSNs6l5Pk,117248
scipy/_lib/_disjoint_set.py,sha256=3EIkZl2z9UajnPRVjSD5H8JszidKURpxHCO580OJGC8,6414
scipy/_lib/_docscrape.py,sha256=7h2MPioiutF3h6mo-pI7usFEBzr10MpRk3sTRKxuyGY,24568
scipy/_lib/_elementwise_iterative_method.py,sha256=RrHdRT0K542u9pPZc_QwS1fzPjc1t4sOqsBQbbJ68Cs,15634
scipy/_lib/_finite_differences.py,sha256=Uu28sJ1PNvgW0Y7ESs0voon5AVYtj0QRrbqsVPjoL70,4317
scipy/_lib/_fpumode.cp311-win_amd64.dll.a,sha256=J8FYl1CjkmBcwU0E1cCE25MuKf5mitaL0IJKnPnyqVw,1560
scipy/_lib/_fpumode.cp311-win_amd64.pyd,sha256=Im2sS8jHHx-Ao3JGLKvx84z_3B5emjmgUbCDl5cnrwo,15872
scipy/_lib/_gcutils.py,sha256=ULLIREutjgyo8dLsvU2hhI0lDGeu5LqX6557X60xPi0,2774
scipy/_lib/_pep440.py,sha256=xSupJHSD_X9EOOUla1iKcNZ9lwxW6qraE3cbHHLunBY,14492
scipy/_lib/_test_ccallback.cp311-win_amd64.dll.a,sha256=35d4a-ltf9AA886LsnYXVAwkVwPvcYmRpvTPM7SJLaw,1640
scipy/_lib/_test_ccallback.cp311-win_amd64.pyd,sha256=4i3jg-ISA2x9_jBok4GZOE5Ri0GB2XdHWfkF7H8XA3o,52224
scipy/_lib/_test_deprecation_call.cp311-win_amd64.dll.a,sha256=7msDLK8BcHXkZ9RvsIV7HuNX48xP91DsjTGUa3FitDE,1724
scipy/_lib/_test_deprecation_call.cp311-win_amd64.pyd,sha256=Q_-lY_ZICRM-ouO7Bk8YrvLB3jF2GE-a2ZGMVeDs-bY,34816
scipy/_lib/_test_deprecation_def.cp311-win_amd64.dll.a,sha256=H2dX3lX3lhlqDrZrUIcmTNMDN5I-tXAKyjb6Hjt8gYo,1712
scipy/_lib/_test_deprecation_def.cp311-win_amd64.pyd,sha256=OY2vXHwwo08EP7s7WdtZpbyITSbtg5y9wm-S19rYZfU,27136
scipy/_lib/_testutils.py,sha256=X7WJCQyP8k446nPpP5jYnHvM7XdYes4SqdlQH8Efagk,12436
scipy/_lib/_threadsafety.py,sha256=HflLz5avQPXKV2Dlf-QmHSkzC5PKdbO2JhWp-cZ_Sl4,1511
scipy/_lib/_tmpdirs.py,sha256=r8sz8dcfBYiPAYondvpGHVVFB2_rB5sajkTeV1gt1fw,2460
scipy/_lib/_uarray/LICENSE,sha256=5MSswx6ChwZtfJldJuPvkCyhl37hh7JgsRwng2ZQOBE,1543
scipy/_lib/_uarray/__init__.py,sha256=pex73GgY7YUWARVgHzYw_Ky2fdAH7M9BcuDMif_hm38,4609
scipy/_lib/_uarray/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/_uarray/__pycache__/_backend.cpython-311.pyc,,
scipy/_lib/_uarray/_backend.py,sha256=u9plJrnv8yIMb0zZyCecmJPp_ZIvUCwQ7Y3H-ajGXhs,21238
scipy/_lib/_uarray/_uarray.cp311-win_amd64.dll.a,sha256=pIjRdxTm_epnerf5rDmMgotXNAMGEhg3aLDgD3A-Bgg,1544
scipy/_lib/_uarray/_uarray.cp311-win_amd64.pyd,sha256=uebIDi8Gxzk7MlvMEbZtnLNuBcHIfAywImJqMzKIkas,233472
scipy/_lib/_util.py,sha256=D-5bU_RlWYWIhQSIaHb305_Eud-7wIyalvwjv2xY4bk,45784
scipy/_lib/array_api_compat/__init__.py,sha256=R73tKtUG2UVuwL_ji-rRQmqymnv59hcOyX7dQPpTdYg,991
scipy/_lib/array_api_compat/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/__pycache__/_internal.cpython-311.pyc,,
scipy/_lib/array_api_compat/_internal.py,sha256=h_DFinHYX8hkUskb3-vE-mVsrE8lAYXhE8VmfTNn1RE,1056
scipy/_lib/array_api_compat/common/__init__.py,sha256=Pcc7izwxYn16qGUghaEELBrpxo-jwxvMA84eAxuAYco,38
scipy/_lib/array_api_compat/common/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_helpers.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_compat/common/_aliases.py,sha256=E6Vo4kQ_IU67slTVIC0BgROPiW7wPlmMW2MctQQtsE0,18430
scipy/_lib/array_api_compat/common/_fft.py,sha256=5pov37o1Fo4kddw3n09UlrPsYM0acM6hfzmt9-1xhCA,4703
scipy/_lib/array_api_compat/common/_helpers.py,sha256=U89R1VPLNYai1-UIPhci-r-I-y1lFi6y1nqetfRbr_Y,24781
scipy/_lib/array_api_compat/common/_linalg.py,sha256=uGSC_xaLUF38QiNKEfeA4TDiwQ1WPlryB8n5x3NpGsY,6298
scipy/_lib/array_api_compat/common/_typing.py,sha256=tg-oqD7YZ8bkUDw06Nz3yzsLbMDycZ1ZSREFSsT7Fds,437
scipy/_lib/array_api_compat/cupy/__init__.py,sha256=-Vaz06bWMFkzF092EoUok7sQ4qcfgXR5evF1OWkJAzE,458
scipy/_lib/array_api_compat/cupy/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_info.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/cupy/_aliases.py,sha256=B7zi5WF880lXzh4l0R2wiuTz4LUWECO9lFyyAzr5A6s,4674
scipy/_lib/array_api_compat/cupy/_info.py,sha256=j3qEIuu9LEBqEbs4MzA3dbxw_2vB9UCqWcQAcFEPGe8,10131
scipy/_lib/array_api_compat/cupy/_typing.py,sha256=VhPA4g6G5Y8acUHqd5XG_vABH_M5mHUajSqFFfesBgM,663
scipy/_lib/array_api_compat/cupy/fft.py,sha256=9Uq43Ykr7VXkYooSFkPDAjgMYv8wC_s-FBrVn3ncSFw,878
scipy/_lib/array_api_compat/cupy/linalg.py,sha256=Oc-wZ7mgrnVMNMxGAr0uK6MFTFx6ctzHgYoEEgM2-vo,1493
scipy/_lib/array_api_compat/dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/array_api_compat/dask/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__init__.py,sha256=akn1emJWQKfHsKd5jg0HPqA1TyhxkBEje8AIEIRszxc,251
scipy/_lib/array_api_compat/dask/array/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/_info.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/dask/array/_aliases.py,sha256=Pea-7ok7Gi1wZBKzIV-EYoA4_9P-B6-AqfthJ72JE-s,6766
scipy/_lib/array_api_compat/dask/array/_info.py,sha256=vxIJKuoKJkQcYm1J8E5y4uc3x4ORFJdL5_8Yo1qqLSo,10755
scipy/_lib/array_api_compat/dask/array/fft.py,sha256=ZWfNcjnM77U0kUpUEx0gFcBsZ4TWvwx2nYnyd3JVa_I,577
scipy/_lib/array_api_compat/dask/array/linalg.py,sha256=yyv0Q1zb8sqLf_AvTpC10wYMCpUfna85XHkIprT80D4,2514
scipy/_lib/array_api_compat/numpy/__init__.py,sha256=cQ3MgL6RddQIAYN7anerIgwVHikoKRXCIn4d0g4AyXc,861
scipy/_lib/array_api_compat/numpy/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_info.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/numpy/_aliases.py,sha256=DLYR8GOSyI6LFzJezWRTgLxi1IqEN1QrSCrmKZaErq8,4626
scipy/_lib/array_api_compat/numpy/_info.py,sha256=-ga-xPPgsySIlUBIe5p-su-zeJsiZeBTVwDFT-kXQA8,10730
scipy/_lib/array_api_compat/numpy/_typing.py,sha256=m3A_ClFAyC17Hbsm77fe-eSfXbOFpx5h9_WggniIN5A,664
scipy/_lib/array_api_compat/numpy/fft.py,sha256=vqmmXzSZkWlC5V9VB1DO4Uhyrr_BVaC6MRZjihSJ3CY,708
scipy/_lib/array_api_compat/numpy/linalg.py,sha256=Y4TL378Z6eRW1qzDgbI114CMdQ6-VNPX31V8xCIZt9k,3346
scipy/_lib/array_api_compat/torch/__init__.py,sha256=6yAjhYSSTTs2AM_AgJjiaogTMG-NPGEpV9XM48Monpg,615
scipy/_lib/array_api_compat/torch/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/_aliases.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/_info.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/fft.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/__pycache__/linalg.cpython-311.pyc,,
scipy/_lib/array_api_compat/torch/_aliases.py,sha256=-KNv8N3W3d4em_rugQxFprTb8Sin6WXjYR0pXb3CyhE,29311
scipy/_lib/array_api_compat/torch/_info.py,sha256=tFe3qs_Ui4UX5Jklnp3JPpqj_TY3sHXYFBUSAZNX-14,11771
scipy/_lib/array_api_compat/torch/fft.py,sha256=DcBZjX0641nT5MZ1IQ0Cjk74fAcjCHzXfNFDiGHQkQc,1880
scipy/_lib/array_api_compat/torch/linalg.py,sha256=WM6mtVS2CXUUtYeDnG--wy5TL0xy5BFyyQQu_a7fbGU,4891
scipy/_lib/array_api_extra/__init__.py,sha256=jHhorYzXyBBkPfmdaqb2kVJZJ8WdhfpukMCkwaMamFY,281
scipy/_lib/array_api_extra/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/array_api_extra/__pycache__/_funcs.cpython-311.pyc,,
scipy/_lib/array_api_extra/__pycache__/_typing.cpython-311.pyc,,
scipy/_lib/array_api_extra/_funcs.py,sha256=ofasbMsUh0m8iWa83wEI-NuaT7i9K8jCIF9lm2qdnxg,15390
scipy/_lib/array_api_extra/_typing.py,sha256=l7EiLON-Kvg61CBqh8xHBuIlqp6Ld-nwAfHznWycHek,201
scipy/_lib/cobyqa/__init__.py,sha256=0JG2qoARCnrJ4xv4L1fvfEHXSI4_IYJPtIT5UbAtvAk,598
scipy/_lib/cobyqa/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/framework.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/main.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/models.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/problem.cpython-311.pyc,,
scipy/_lib/cobyqa/__pycache__/settings.cpython-311.pyc,,
scipy/_lib/cobyqa/framework.py,sha256=nIzsKelKL8-xa_QMv8QKTrbdvSsk5fLRmCOOx6YyhXg,40140
scipy/_lib/cobyqa/main.py,sha256=HXihzfF2vCRGUHsBgxBJOzY7F7ecFmrDqYs2HwzaErQ,59033
scipy/_lib/cobyqa/models.py,sha256=XPQj8dX2f_5tJHGpn7siHmrJ6Zp8Y2-3gK54RiYvWjw,52185
scipy/_lib/cobyqa/problem.py,sha256=rZyUJqIJcsuAkejQK_xJMEPfkMjOE8uHk1X_lB6mb8A,41499
scipy/_lib/cobyqa/settings.py,sha256=fWu0NPyFiBE-8No4R9B4pUKSjEbuSlaa0AWtHIzHblA,3958
scipy/_lib/cobyqa/subsolvers/__init__.py,sha256=96yleneJh-YtMtCa8tG8cfsNM-Y_1lF30jJ5tnIKIQg,355
scipy/_lib/cobyqa/subsolvers/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/cobyqa/subsolvers/__pycache__/geometry.cpython-311.pyc,,
scipy/_lib/cobyqa/subsolvers/__pycache__/optim.cpython-311.pyc,,
scipy/_lib/cobyqa/subsolvers/geometry.py,sha256=1jO30o2Nm4BiKpb-GAv8Uqj1SnDNLpo9J7ECoOUdP3o,14560
scipy/_lib/cobyqa/subsolvers/optim.py,sha256=-tJVfZcS66vfQYri293E4Bx710VqYT-N00bYN6vpo9I,46715
scipy/_lib/cobyqa/utils/__init__.py,sha256=dyc27b5M6bGiH7f1laeaoqaQeEKlKvFevVghBV7LrtY,377
scipy/_lib/cobyqa/utils/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/exceptions.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/math.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/__pycache__/versions.cpython-311.pyc,,
scipy/_lib/cobyqa/utils/exceptions.py,sha256=rFMjALTKz9xNnb4pTS5v_VIXqh0T7QpwlkK8YUeN000,505
scipy/_lib/cobyqa/utils/math.py,sha256=QhsfzOYAJnLtViuMAXKNQwpRd5ZKMNs_faeJ3TKQdnw,1688
scipy/_lib/cobyqa/utils/versions.py,sha256=S2JG4-zryLBoBf2yzVlIRs1sW6eMUeWFfcjWn2D39jg,1532
scipy/_lib/decorator.py,sha256=hjvTnMvFr_5xY6DNZRHUsZ-prf6eb_iwAV14K-29Vuk,15420
scipy/_lib/deprecation.py,sha256=Sk3aEvBPTml1uJVvM--nQqhB2GG82LjnYhDmZWDnN0w,10114
scipy/_lib/doccer.py,sha256=3W-P-evEL9DIR7BlowLhI6VdaLMSEVdX6yxA2gJ1up4,11279
scipy/_lib/messagestream.cp311-win_amd64.dll.a,sha256=kHKhyQ76vd4qGDQzZ4NmLgMHIBXzPO-z2R3h3Jil1Qw,1616
scipy/_lib/messagestream.cp311-win_amd64.pyd,sha256=CXIRoceFaEFLcn1oGSAjJSY4L5qfn5TcSbAbC_sOiEU,66048
scipy/_lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/_lib/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__gcutils.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__pep440.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__testutils.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__threadsafety.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test__util.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_array_api.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_bunch.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_ccallback.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_config.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_deprecation.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_doccer.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_import_cycles.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_public_api.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_scipy_version.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_tmpdirs.cpython-311.pyc,,
scipy/_lib/tests/__pycache__/test_warnings.cpython-311.pyc,,
scipy/_lib/tests/test__gcutils.py,sha256=vdtwdwiItw_VvyM1NTdbT0G4BxxEBFdTWQ7K67NFuzQ,3839
scipy/_lib/tests/test__pep440.py,sha256=4AS6UhkhxfVqvZfZmxM0pgIOvQVbAjPdWN3YtgfXWkY,2344
scipy/_lib/tests/test__testutils.py,sha256=cI6oLinJsZDXIi_pmPMTVAYTvepddvhYVGEMd2MlW2Q,832
scipy/_lib/tests/test__threadsafety.py,sha256=nnAypNwYK4YNe2s3xcYfGslLKXqdcrOeza73EeybiAQ,1373
scipy/_lib/tests/test__util.py,sha256=wf3g1W1r_j2EHhw0sOCD38pJWFxHjquc4Ye7Q0IJ77c,25302
scipy/_lib/tests/test_array_api.py,sha256=xBSa018a2p8sHUt-M-5xwM-Elg9jRUB2K7PqZMjLlGQ,8266
scipy/_lib/tests/test_bunch.py,sha256=yZmGHnJ-qBIMP-8TmnyvK7OGiXRzHzbRHCpMwIsmsk0,6330
scipy/_lib/tests/test_ccallback.py,sha256=klbEMLd28QAUyzGp6Z89Fr6FzV6jKnmuQumzpe4bXco,6379
scipy/_lib/tests/test_config.py,sha256=gk6SUpFQ8CIJ5mrnn2wino0IPryTF2PM2M891PG5Z74,1320
scipy/_lib/tests/test_deprecation.py,sha256=dii6UZ3ZiUJ0Lu4ubX5SkC4R9NkwBireWJOP5b9qJSA,400
scipy/_lib/tests/test_doccer.py,sha256=wmoKV2T7y_6K0P2RtN8d6IvxXCvuctzQkjgmVgRMSnc,4196
scipy/_lib/tests/test_import_cycles.py,sha256=9PxfWvQUdWHRPxjp74H-F8AAgb7XL4niOUfGusfVL3E,604
scipy/_lib/tests/test_public_api.py,sha256=cJ1ukk6rb1GZytkEFJ-WvM1OybBY0daHoTBJUhL5hE8,18535
scipy/_lib/tests/test_scipy_version.py,sha256=R_wW_ajgGbFq0cl3N88Msg9bYUu6_qJPHOywArtT-XA,946
scipy/_lib/tests/test_tmpdirs.py,sha256=wNCRS8MSRZZPe-2g4U_eCUHGYF6uA9SBsH6pzAARnec,1385
scipy/_lib/tests/test_warnings.py,sha256=9v2UdRO6GQxHfUzubef-AOaTGgDl8DaBQIRFhW4Ul_Q,5086
scipy/_lib/uarray.py,sha256=qXvvUluJiQJwLybyo5ZZtGGWa_o2T0mSvojeu8t_IkQ,846
scipy/cluster/__init__.py,sha256=AWNDaf2OiXXDbfg_d9SqDxhV7DE6o36K3u0zw5O7jXA,911
scipy/cluster/__pycache__/__init__.cpython-311.pyc,,
scipy/cluster/__pycache__/hierarchy.cpython-311.pyc,,
scipy/cluster/__pycache__/vq.cpython-311.pyc,,
scipy/cluster/_hierarchy.cp311-win_amd64.dll.a,sha256=-CmBq1c1jFBx_T4vxdJafBMeQlEFkbWDSbFWzD_WLoU,1580
scipy/cluster/_hierarchy.cp311-win_amd64.pyd,sha256=zeLgIBOc7bUWyfrEw8phcaDotguLOFFDoFMLZfHmL9I,380928
scipy/cluster/_optimal_leaf_ordering.cp311-win_amd64.dll.a,sha256=TBYcgbFXYI3A807cHmTgavqE4NCzUF0lhSMhFTx_Bw8,1724
scipy/cluster/_optimal_leaf_ordering.cp311-win_amd64.pyd,sha256=_RperXg936-PUfE2a-8-ZCJaWPiQoNZu0WTl97pZjUw,327168
scipy/cluster/_vq.cp311-win_amd64.dll.a,sha256=v-nOcLzgAdF33s4gvCExXC-cXI-cykXuZZfHAynmUVE,1496
scipy/cluster/_vq.cp311-win_amd64.pyd,sha256=1aq9RaBJp4B6D__gFyzFmj9orEUzsqeGbCVrSF3fFL0,110080
scipy/cluster/hierarchy.py,sha256=PfgHZY-VOQzEHWeg522uxGU5WAGGlyIg0zKyHra1pGw,153256
scipy/cluster/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/cluster/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/hierarchy_test_data.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/test_disjoint_set.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/test_hierarchy.cpython-311.pyc,,
scipy/cluster/tests/__pycache__/test_vq.cpython-311.pyc,,
scipy/cluster/tests/hierarchy_test_data.py,sha256=lvVMk2L5iEKlhX32aFRBJ3clecx6AXLYw75Epl07OBo,6995
scipy/cluster/tests/test_disjoint_set.py,sha256=fczIL8hK_ICE23j54dwEdn9KBOipPnpxAhWK7xj8X4o,5727
scipy/cluster/tests/test_hierarchy.py,sha256=PhzKaOXYlbM9W7UWc_KQBWoPCBHzZVCpozruERI6i8I,53409
scipy/cluster/tests/test_vq.py,sha256=mPyIJ04n0NKMFsBoh_6gNWga65VBQUHIKoS4qtqHLrQ,19423
scipy/cluster/vq.py,sha256=K6amOwDlwTHuDE-6yJ6jLdd5tENzt9PXBd7pdlC1QqI,31376
scipy/conftest.py,sha256=fUIpbuo6nIOkji_m3GJ6qMFhAHaEutGpBzW1z7nhfkk,22579
scipy/constants/__init__.py,sha256=dL-gJZdTq_191I1mLrl6SoSvi_Ewk39gcJGylOG3fVo,15197
scipy/constants/__pycache__/__init__.cpython-311.pyc,,
scipy/constants/__pycache__/_codata.cpython-311.pyc,,
scipy/constants/__pycache__/_constants.cpython-311.pyc,,
scipy/constants/__pycache__/codata.cpython-311.pyc,,
scipy/constants/__pycache__/constants.cpython-311.pyc,,
scipy/constants/_codata.py,sha256=ZyC5LwdzQT2durBSoqND6andYDLo8M2RbJZ_3UVT0wM,204620
scipy/constants/_constants.py,sha256=zGmj18NexEIIII3k2RHTFBhZ8tr8JqfxPXWf0o65WxU,10863
scipy/constants/codata.py,sha256=Fayz-HkpNqk8aOadBz7AfFpKinS4JcYhKBm_JUweY-o,635
scipy/constants/constants.py,sha256=vdC_jDTQZWQlrkYZY0Uj7NKO2AtBwH5mc28Jhy7Ur9s,2303
scipy/constants/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/constants/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/constants/tests/__pycache__/test_codata.cpython-311.pyc,,
scipy/constants/tests/__pycache__/test_constants.cpython-311.pyc,,
scipy/constants/tests/test_codata.py,sha256=Yf2PYeJ8gyO29urHWJ4A5I9FX6YZlaJmjQv9dBpG_kQ,2919
scipy/constants/tests/test_constants.py,sha256=CsGiMtiRNjSUCGggcJh6RB5sqhbkE7H2axGkkNFI5i0,4765
scipy/datasets/__init__.py,sha256=EUFFXPZhchasAFFWACy12Sz1A6w3wgUXBICgfTmAaHE,2892
scipy/datasets/__pycache__/__init__.cpython-311.pyc,,
scipy/datasets/__pycache__/_download_all.cpython-311.pyc,,
scipy/datasets/__pycache__/_fetchers.cpython-311.pyc,,
scipy/datasets/__pycache__/_registry.cpython-311.pyc,,
scipy/datasets/__pycache__/_utils.cpython-311.pyc,,
scipy/datasets/_download_all.py,sha256=i-fMQWgAfvOgCEoEabsd68iUYDki-4OUfPjjcGLHRDI,1758
scipy/datasets/_fetchers.py,sha256=JjCsELocalukkmwBlngRmOpnSfPIGXp2vU8-bV3OtcU,6954
scipy/datasets/_registry.py,sha256=3_ZNYpe3lCXvUGalpNsrhRwKcnFA2P6yWV7MFdKb2Hw,1098
scipy/datasets/_utils.py,sha256=7bK_NVCoURt-HDGe7I0iBGLEdcrLIU5eJuSjJqAkgJg,3048
scipy/datasets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/datasets/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/datasets/tests/__pycache__/test_data.cpython-311.pyc,,
scipy/datasets/tests/test_data.py,sha256=op7cichKh15h3N0uSyJ1h3Mt-5q_ipZrIVILJjlZpmw,4341
scipy/differentiate/__init__.py,sha256=s67FqxVRyuL-VKPjOxeahlOLVlHCwHp5f_6xEmGmHsg,648
scipy/differentiate/__pycache__/__init__.cpython-311.pyc,,
scipy/differentiate/__pycache__/_differentiate.cpython-311.pyc,,
scipy/differentiate/_differentiate.py,sha256=cA_wEXqwlNdi_isR-SYjv_C0gt-bkP5EiC7WSi_8C8I,51724
scipy/differentiate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/differentiate/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/differentiate/tests/__pycache__/test_differentiate.cpython-311.pyc,,
scipy/differentiate/tests/test_differentiate.py,sha256=1tdNms6h4Ygjf1QVIRrTPYMOEIuSuv-16v-LlC0Xk60,28734
scipy/fft/__init__.py,sha256=Sn1qqeuX6MkMiU7eRyFMEE3ekkecR5Sht-WK-dWaPvk,3746
scipy/fft/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/__pycache__/_backend.cpython-311.pyc,,
scipy/fft/__pycache__/_basic.cpython-311.pyc,,
scipy/fft/__pycache__/_basic_backend.cpython-311.pyc,,
scipy/fft/__pycache__/_debug_backends.cpython-311.pyc,,
scipy/fft/__pycache__/_fftlog.cpython-311.pyc,,
scipy/fft/__pycache__/_fftlog_backend.cpython-311.pyc,,
scipy/fft/__pycache__/_helper.cpython-311.pyc,,
scipy/fft/__pycache__/_realtransforms.cpython-311.pyc,,
scipy/fft/__pycache__/_realtransforms_backend.cpython-311.pyc,,
scipy/fft/_backend.py,sha256=ySd0v6IodaTh-_kjfEOogdlfCsJ3rdcRLqv-ZU8NkTM,6740
scipy/fft/_basic.py,sha256=pSqks7ZcODqlyWVorO1pg3C_GyfcQxVdgUSHvNTTHmg,64627
scipy/fft/_basic_backend.py,sha256=eLhjbNBFsM40DtuTtofDo_F20l8kZ5vuZbfB5aQ1rVk,7644
scipy/fft/_debug_backends.py,sha256=R0zCc_CgDubYnaGbQGjk0GTmd5HlYFpBLw7WSlqljpQ,620
scipy/fft/_fftlog.py,sha256=NnJ5-3iwtWwapt-yLhI9ySLqB_cOJvOnxOuDm8XJQa0,8087
scipy/fft/_fftlog_backend.py,sha256=tXS8zJyOD1aQeGpT2ac_7l8q6gmQ3brLm7uphdeYB14,5504
scipy/fft/_helper.py,sha256=zESzcOKqLJSM0cBjhZPp4Yh0kMjBSjZnAy7eE_rv0O4,12764
scipy/fft/_pocketfft/LICENSE.md,sha256=wEZhaFz_nYA1_hxMOrNX5_djPK_AanPp2Qw8X-3oqGo,1523
scipy/fft/_pocketfft/__init__.py,sha256=WKXb59wx9DK9zXq9DwKchp-zg1SuJIynTQ_POQrFXxo,216
scipy/fft/_pocketfft/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/_pocketfft/__pycache__/basic.cpython-311.pyc,,
scipy/fft/_pocketfft/__pycache__/helper.cpython-311.pyc,,
scipy/fft/_pocketfft/__pycache__/realtransforms.cpython-311.pyc,,
scipy/fft/_pocketfft/basic.py,sha256=dST8PBFhoiOR1Kj1j3CcjMC0p7KFxTA9qDr1oN2YrFw,8389
scipy/fft/_pocketfft/helper.py,sha256=ecKFAIxeXXxMJllrRIbkPdETQ6Sdq552ozKJEaCG80g,6062
scipy/fft/_pocketfft/pypocketfft.cp311-win_amd64.dll.a,sha256=YL6V6dH_noMulLIHTdFMFRjYkB6CTfa7PnkbhoV436Y,1592
scipy/fft/_pocketfft/pypocketfft.cp311-win_amd64.pyd,sha256=koBJGPEc6ybCD9qqAePeX6M3EGtYHxcG0b8rt5OpV-0,1082368
scipy/fft/_pocketfft/realtransforms.py,sha256=nk1e31laxa9920AeBgZgb1vZSXIpoILwWqSI028HCyQ,3453
scipy/fft/_pocketfft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/_pocketfft/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/fft/_pocketfft/tests/__pycache__/test_real_transforms.cpython-311.pyc,,
scipy/fft/_pocketfft/tests/test_basic.py,sha256=D3yVnCydBZmdhSnhuirTEfLg2kmV1ACq5nLcrFwZyzE,36728
scipy/fft/_pocketfft/tests/test_real_transforms.py,sha256=35KKSA1qEE_DhvxLE8y_6EyZ_Cv0Y5BUk8hgQ8NpmTg,17384
scipy/fft/_realtransforms.py,sha256=ba4SZIlpA405Ujt5lEo2RngO3zsoJJS0259t7nC23_s,26079
scipy/fft/_realtransforms_backend.py,sha256=hJ3LkFOvggibVF_E36fM9R3ZqAJHHicYrGUceH8B81g,2452
scipy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fft/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/fft/tests/__pycache__/mock_backend.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_backend.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_fftlog.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_helper.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_multithreading.cpython-311.pyc,,
scipy/fft/tests/__pycache__/test_real_transforms.cpython-311.pyc,,
scipy/fft/tests/mock_backend.py,sha256=mnU42TLolLB-fNi3tzxY_QFK9sl4f1dNswNNr6QwpIE,2781
scipy/fft/tests/test_backend.py,sha256=wqI0cuYwwEj_8eTFDjqoOXVioCAExf1YvJ6VmhsFU9c,4383
scipy/fft/tests/test_basic.py,sha256=YNr6pB4rKEFTw9w8RHZ_UPca4_elF5wrklQuMzG-HVA,21221
scipy/fft/tests/test_fftlog.py,sha256=66UmIjYC-A0R8M4qj5SQbWwDMLm1xAp2thsWprEJz34,7568
scipy/fft/tests/test_helper.py,sha256=tRGUMql16RWrBmBnMpfJo4uo4XgoytWqYx3ujTBs-Fg,20761
scipy/fft/tests/test_multithreading.py,sha256=hMqYEawnqaoCS3fp6dOSLBn1l4OR6D7txckjod8vipc,2234
scipy/fft/tests/test_real_transforms.py,sha256=RKMQPzBhBCWX8anBBsOL5V5gmfy368XNgPimGLyZZ_o,9536
scipy/fftpack/__init__.py,sha256=NKSnRJ6EMDP3nDxGSJMyd2PJBB2ytMAO01Kq884NXXo,3258
scipy/fftpack/__pycache__/__init__.cpython-311.pyc,,
scipy/fftpack/__pycache__/_basic.cpython-311.pyc,,
scipy/fftpack/__pycache__/_helper.cpython-311.pyc,,
scipy/fftpack/__pycache__/_pseudo_diffs.cpython-311.pyc,,
scipy/fftpack/__pycache__/_realtransforms.cpython-311.pyc,,
scipy/fftpack/__pycache__/basic.cpython-311.pyc,,
scipy/fftpack/__pycache__/helper.cpython-311.pyc,,
scipy/fftpack/__pycache__/pseudo_diffs.cpython-311.pyc,,
scipy/fftpack/__pycache__/realtransforms.cpython-311.pyc,,
scipy/fftpack/_basic.py,sha256=6AFPL2JeBvZsIZV-C3kAmvG3ybQ8XtiRLSJQ9Go9L2E,13526
scipy/fftpack/_helper.py,sha256=krb3yRwgVmF-r3t-n-FGyNa8h5qhfiP4p0UGxQ39BNo,3463
scipy/fftpack/_pseudo_diffs.py,sha256=MIvGWp5wHl1ykkBheub658tnDjl6_76EEx-aM7o11XM,16490
scipy/fftpack/_realtransforms.py,sha256=RT4ScAxB-Em_Dd8j2Ca7VKQfUSOHpwrYADrjKQXBifk,19820
scipy/fftpack/basic.py,sha256=FFn2KxrsmC6IsOQdjcoVr8Nvrlng2FRiv7gNeT1ZrY4,597
scipy/fftpack/convolve.cp311-win_amd64.dll.a,sha256=KQ9O4Nk7TcJWZM9f8dFXs0NKK1HumG5Cie9ktk8kbMo,1560
scipy/fftpack/convolve.cp311-win_amd64.pyd,sha256=HuUPQ1qX3fdsu8o4BWmxTZ5R9uTLOLCTS6zhzzmr33w,250880
scipy/fftpack/helper.py,sha256=1b1b278FWyTc2MeAjeLFB8eyV76pRxOigGtBUvCp_lo,599
scipy/fftpack/pseudo_diffs.py,sha256=oLCcXufpR_wAj4TKj3OTqyIEGXag1xSl_ueO83XspMI,680
scipy/fftpack/realtransforms.py,sha256=oUJXNb5KAyS4k8xubnE7hGE9BpLCcdkk_iiReyB8OOE,614
scipy/fftpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/fftpack/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_helper.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_import.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_pseudo_diffs.cpython-311.pyc,,
scipy/fftpack/tests/__pycache__/test_real_transforms.cpython-311.pyc,,
scipy/fftpack/tests/fftw_double_ref.npz,sha256=pgxklBW2RSI5JNg0LMxcCXgByGkBKHo2nlP8kln17E4,162120
scipy/fftpack/tests/fftw_longdouble_ref.npz,sha256=pAbL1NrQTQxZ3Tj1RBb7SUJMgiKcGgdLakTsDN4gAOM,296072
scipy/fftpack/tests/fftw_single_ref.npz,sha256=J2qRQTGOb8NuSrb_VKYbZAVO-ISbZg8XNZ5fVBtDxSY,95144
scipy/fftpack/tests/test.npz,sha256=Nt6ASiLY_eoFRZDOSd3zyFmDi32JGTxWs7y2YMv0N5c,11968
scipy/fftpack/tests/test_basic.py,sha256=NsXS9xTiHTHwryuigaHgo3bLSk__CnrF4cHgVSqHSKk,31433
scipy/fftpack/tests/test_helper.py,sha256=HuCAP5esSm4lMnw9f9i-ByJj1DURntgC64s2fX5RVIg,1729
scipy/fftpack/tests/test_import.py,sha256=v63sC7YHLiOcaMWZ75_t0Nasz2Q8Tq-lo7awD4CINwI,1189
scipy/fftpack/tests/test_pseudo_diffs.py,sha256=2NGiR5OlUGn6ybqbzLD1WXTltWpfBuv3e0EW8xk1cBU,14121
scipy/fftpack/tests/test_real_transforms.py,sha256=RePgJ0pnk2MI4imViDgrLtUmwfJ3Us2EYYe8Rd2UdLc,25322
scipy/integrate/__init__.py,sha256=RIEI7skWhbt4SVbMlV4BnP4EEOAY8AVy-iJ2K-ovp44,4495
scipy/integrate/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/__pycache__/_bvp.cpython-311.pyc,,
scipy/integrate/__pycache__/_cubature.cpython-311.pyc,,
scipy/integrate/__pycache__/_lebedev.cpython-311.pyc,,
scipy/integrate/__pycache__/_ode.cpython-311.pyc,,
scipy/integrate/__pycache__/_odepack_py.cpython-311.pyc,,
scipy/integrate/__pycache__/_quad_vec.cpython-311.pyc,,
scipy/integrate/__pycache__/_quadpack_py.cpython-311.pyc,,
scipy/integrate/__pycache__/_quadrature.cpython-311.pyc,,
scipy/integrate/__pycache__/_tanhsinh.cpython-311.pyc,,
scipy/integrate/__pycache__/dop.cpython-311.pyc,,
scipy/integrate/__pycache__/lsoda.cpython-311.pyc,,
scipy/integrate/__pycache__/odepack.cpython-311.pyc,,
scipy/integrate/__pycache__/quadpack.cpython-311.pyc,,
scipy/integrate/__pycache__/vode.cpython-311.pyc,,
scipy/integrate/_bvp.py,sha256=WvVN9nLRN7S3RyIg6skZ4qhpSPjcHOLRS4fY_Bv50V0,42051
scipy/integrate/_cubature.py,sha256=x6AMR3x-Qw3nQ2IXTtc-O9UhuRQAutcieox1Ebkyj2s,26399
scipy/integrate/_dop.cp311-win_amd64.dll.a,sha256=QQ2FQxa9iGz4dxeAYdT2gegML8qxNJjFzIteJ_qXi3E,1512
scipy/integrate/_dop.cp311-win_amd64.pyd,sha256=6evUrKBWIPvK76_n4uTnW9R9l0mpRrTG6aeIMfuvqOI,433664
scipy/integrate/_ivp/__init__.py,sha256=XVoxnj-1q1Xm4StWijukIoIrqc6Ny1Dxi625GurInWs,264
scipy/integrate/_ivp/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/base.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/bdf.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/common.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/dop853_coefficients.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/ivp.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/lsoda.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/radau.cpython-311.pyc,,
scipy/integrate/_ivp/__pycache__/rk.cpython-311.pyc,,
scipy/integrate/_ivp/base.py,sha256=efUVvU8k5dFgglCta7f5ZiCMyTU4XN38JMfiKLQcYFQ,10585
scipy/integrate/_ivp/bdf.py,sha256=i_XH2ClhYxdjZuunYZX5h7HfqLbNQWib2ep8_KrM3Q0,17979
scipy/integrate/_ivp/common.py,sha256=ad9h8tAf2fzoxqtvSYE3u4kXdaf381sl0h7vJztFvqc,16196
scipy/integrate/_ivp/dop853_coefficients.py,sha256=4oMhmg8eXyzDeSSrlh2dJJMvaabWn-aO44S3wx8Ig5o,7430
scipy/integrate/_ivp/ivp.py,sha256=FF2eM8zxQ_xOk5j1CBQWs98eSDgdHFtdcZVn71gLpCU,32498
scipy/integrate/_ivp/lsoda.py,sha256=95NTm0zyHR5zFH5IeKo9kEWtDkdR3Mdfrbubnsf4Awo,10151
scipy/integrate/_ivp/radau.py,sha256=VoxSVlIU3osykkQ_YXv3BSXcQ24hMX8-RiKgtfHcA0E,20248
scipy/integrate/_ivp/rk.py,sha256=F8juhimgqsq5y6-9kpT_BcFqGOhv8PaD__LZcVSonr8,23401
scipy/integrate/_ivp/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/_ivp/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_ivp.cpython-311.pyc,,
scipy/integrate/_ivp/tests/__pycache__/test_rk.cpython-311.pyc,,
scipy/integrate/_ivp/tests/test_ivp.py,sha256=TROJj-ueTuEMBzneCLSftR4JjKcE33EL3MDMUQywCtE,44110
scipy/integrate/_ivp/tests/test_rk.py,sha256=_8nQKtotVvR218br-0mNSQSEZ-ST4L2Tgaz5xKXHgJw,1363
scipy/integrate/_lebedev.py,sha256=QfCd7zmEI4jJjPGBvM6hMU55gk7nENOElNSxFdYWJaU,267474
scipy/integrate/_lsoda.cp311-win_amd64.dll.a,sha256=Xpv7xyYKFr0rt1OBafdnMTbMkdCmoD3S6HrExEV2krE,1532
scipy/integrate/_lsoda.cp311-win_amd64.pyd,sha256=7l39Y-4SE7Tcvem8JdPS78av8CxAguduNEhss9zBrTE,554496
scipy/integrate/_ode.py,sha256=Ipa3P3FeO_qfc2MceUcB1rU1eXlh7BzBepyPAqfP7oE,49687
scipy/integrate/_odepack.cp311-win_amd64.dll.a,sha256=kmYVEOU_9kLZK2Me3fkd-ccS5JHRbv55TaUVK1UzGoc,1560
scipy/integrate/_odepack.cp311-win_amd64.pyd,sha256=QJoh07EY1fhHPHFZzk8yfau8VzEFkJe_vQOrLY5is8A,534528
scipy/integrate/_odepack_py.py,sha256=HkZMTdAlbnO2-ti2gQ3EracLPuUoygvRE4A5z8uoxag,11504
scipy/integrate/_quad_vec.py,sha256=Iy28dNWhUEbhndMs8S6XcLINeFxUmEX_n4SsYB-skAk,22706
scipy/integrate/_quadpack.cp311-win_amd64.dll.a,sha256=bYjQz8dLqWMhourBb8adhli6YSsg1ktGOYgMBbwpa08,1568
scipy/integrate/_quadpack.cp311-win_amd64.pyd,sha256=gYnzxvjAYk2DezbFSFvPZ8Opk5NzBDngQ7ldVCyiVbI,142848
scipy/integrate/_quadpack_py.py,sha256=PuGadBLT22fRcV4kyseOy3cHFg1zcN6wIZfrhaXvL4U,54529
scipy/integrate/_quadrature.py,sha256=DaQC2HknMnezvY0JWEr3GIOS3QSWr8jNVMgu2rr6GW8,49243
scipy/integrate/_rules/__init__.py,sha256=HFHYDYs5UOQljteKeDXGkSFb-T9ehI3N3gFWE8QK6vQ,340
scipy/integrate/_rules/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/_rules/__pycache__/_base.cpython-311.pyc,,
scipy/integrate/_rules/__pycache__/_gauss_kronrod.cpython-311.pyc,,
scipy/integrate/_rules/__pycache__/_gauss_legendre.cpython-311.pyc,,
scipy/integrate/_rules/__pycache__/_genz_malik.cpython-311.pyc,,
scipy/integrate/_rules/_base.py,sha256=xCv7oPeBsUOZ30RGAC53P5o3s6PoQIa-4htS8MthpGs,18449
scipy/integrate/_rules/_gauss_kronrod.py,sha256=tJ5h1xaYEdiJ_5wCQsKCvEaXB0yjAl9luI78rg9vXxM,8675
scipy/integrate/_rules/_gauss_legendre.py,sha256=J3mKoTtZlvxUdHnFYw2dPSsWAzdVXovV3f7UQnT5BT0,1795
scipy/integrate/_rules/_genz_malik.py,sha256=dqe9DLj0DhNttRn4CTtp0f41sZb8b1uBNSM7CeBXrJo,7518
scipy/integrate/_tanhsinh.py,sha256=t77_s8DGvL2mfa8Hjpf3rLOuAphaz3wE26bchdvFgNo,62724
scipy/integrate/_test_multivariate.cp311-win_amd64.dll.a,sha256=rOmdjZrm6Zw6W_4ooKGxp_QibnJmoTEtc8NqDnIjGfk,1676
scipy/integrate/_test_multivariate.cp311-win_amd64.pyd,sha256=FE2BmaLSZ2Wos5ltS-rCjMHI7r2vgsgJS1wxAAxS8KM,17920
scipy/integrate/_test_odeint_banded.cp311-win_amd64.dll.a,sha256=uH9oQTHuwJPBsWZIkLW99n4fBWUcHYIDva5DlDsqiNk,1688
scipy/integrate/_test_odeint_banded.cp311-win_amd64.pyd,sha256=OylyxE16sO047wJ1pT6CCgbeX6zA9eLtowby1EVXPzI,556544
scipy/integrate/_vode.cp311-win_amd64.dll.a,sha256=dMo_BnKehwSUHuvVQpBQjgn2Ir9Arw90nJIULeXgejk,1520
scipy/integrate/_vode.cp311-win_amd64.pyd,sha256=5U6yWRzAZpgs7HRPmEr5MmVOotthUjCo26se7kXjdGI,615936
scipy/integrate/dop.py,sha256=XFCkBLyUhdNiEb31DGYdiiwYvmkS7FOwUC4HS2RA_g0,437
scipy/integrate/lsoda.py,sha256=KtndEiRbVPejH0aNBaj3mSHWayXP-wqXSoMZJRPWhAg,451
scipy/integrate/odepack.py,sha256=TxsXidD25atNMK5kKXD-FyNCLXnzrwCdVB1hP4aLSQ8,562
scipy/integrate/quadpack.py,sha256=0DHbM39zByxQkBR11xCokuwuJU41YT9bI74hS1pIrXI,627
scipy/integrate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/integrate/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test__quad_vec.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_banded_ode_solvers.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_bvp.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_cubature.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_integrate.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_odeint_jac.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_quadpack.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_quadrature.cpython-311.pyc,,
scipy/integrate/tests/__pycache__/test_tanhsinh.cpython-311.pyc,,
scipy/integrate/tests/test__quad_vec.py,sha256=N23D4XUzgY7dCHK7ydLV_VIxYlApGm6_yts-X1_C7lc,6733
scipy/integrate/tests/test_banded_ode_solvers.py,sha256=q2cghGTC8aWuVjGG_b8Q2EwYydKIrvZ8auWY29_wQZY,6948
scipy/integrate/tests/test_bvp.py,sha256=2Wya2CHkcCvnS3tnzjrqOdTbgc0udhb78IHIkyxP8g4,20937
scipy/integrate/tests/test_cubature.py,sha256=51Yq_y90zFfXZa-EdDTfKJFZv5E_xPyNdnuNWEWbKdY,38407
scipy/integrate/tests/test_integrate.py,sha256=AU-GzheWyINS5II5bJkl0Ym7TR_w0q5vJQKobiUm1ds,25451
scipy/integrate/tests/test_odeint_jac.py,sha256=XedvNm_tVVuiQhUiL_5_OfPe5nDOEc821vsqiLZtm8c,1890
scipy/integrate/tests/test_quadpack.py,sha256=gxhN-laKMIGuszYdOPBYYgBIhOpykikHsTTHdBw27Sw,28746
scipy/integrate/tests/test_quadrature.py,sha256=IKY1WppsV-TPFaZFdGFVwNcegib83OIA9MJm0dKlH78,28980
scipy/integrate/tests/test_tanhsinh.py,sha256=_cMdI_Kh-_A7E30NWfN9cN_7ISQuuWWaTIk7ACx72lU,45963
scipy/integrate/vode.py,sha256=Gkx0nK3_UbOznSvdghjCtUQx4qpSaZzB6rgltKjP6UU,439
scipy/interpolate/__init__.py,sha256=uIbUFGoe7zQO8-0TmlvcxVsi6LIhV3LM37fH9Lje73U,4033
scipy/interpolate/__pycache__/__init__.cpython-311.pyc,,
scipy/interpolate/__pycache__/_bary_rational.cpython-311.pyc,,
scipy/interpolate/__pycache__/_bsplines.cpython-311.pyc,,
scipy/interpolate/__pycache__/_cubic.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack2.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack_impl.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack_py.cpython-311.pyc,,
scipy/interpolate/__pycache__/_fitpack_repro.cpython-311.pyc,,
scipy/interpolate/__pycache__/_interpolate.cpython-311.pyc,,
scipy/interpolate/__pycache__/_ndbspline.cpython-311.pyc,,
scipy/interpolate/__pycache__/_ndgriddata.cpython-311.pyc,,
scipy/interpolate/__pycache__/_pade.cpython-311.pyc,,
scipy/interpolate/__pycache__/_polyint.cpython-311.pyc,,
scipy/interpolate/__pycache__/_rbf.cpython-311.pyc,,
scipy/interpolate/__pycache__/_rbfinterp.cpython-311.pyc,,
scipy/interpolate/__pycache__/_rgi.cpython-311.pyc,,
scipy/interpolate/__pycache__/dfitpack.cpython-311.pyc,,
scipy/interpolate/__pycache__/fitpack.cpython-311.pyc,,
scipy/interpolate/__pycache__/fitpack2.cpython-311.pyc,,
scipy/interpolate/__pycache__/interpnd.cpython-311.pyc,,
scipy/interpolate/__pycache__/interpolate.cpython-311.pyc,,
scipy/interpolate/__pycache__/ndgriddata.cpython-311.pyc,,
scipy/interpolate/__pycache__/polyint.cpython-311.pyc,,
scipy/interpolate/__pycache__/rbf.cpython-311.pyc,,
scipy/interpolate/_bary_rational.py,sha256=MWfs5lh04S55qWoWXh0XDm38s3aJw7mzjTwd2buliWA,28580
scipy/interpolate/_bspl.cp311-win_amd64.dll.a,sha256=IPlBJ8aPuR7FjKb3Vo_NoXDYWMR72_Y6Qka8C2J1SgA,1520
scipy/interpolate/_bspl.cp311-win_amd64.pyd,sha256=NexZvRtrcYs_DXqhHkDot6vA5gJ2eIroHe79iK7hSac,1175040
scipy/interpolate/_bsplines.py,sha256=39LzKMFeBOvIQ2HTHc5paRDFvo7bd0-D8-Ou2hT-Zrk,85109
scipy/interpolate/_cubic.py,sha256=wxEn1VcIpNJ5Rur6lcupl7Ont21wEImX23Z1jcvDFJI,38685
scipy/interpolate/_dfitpack.cp311-win_amd64.dll.a,sha256=fRMySZk0nCLVZeY_sn-m01tbeNowTSiSMDLUZASvURU,1568
scipy/interpolate/_dfitpack.cp311-win_amd64.pyd,sha256=4VcyyrBpe8SnmbZcraWsILSMXc5UxWgesQkRfRVM26E,663552
scipy/interpolate/_dierckx.cp311-win_amd64.dll.a,sha256=Qlii-fXKlh9_UpFVjH51zwLb1neOxcURL5CBM2x1uuQ,1560
scipy/interpolate/_dierckx.cp311-win_amd64.pyd,sha256=JEgaN8hsIuAaJd8Nwcer63vrMI3rv1dHAaDcML8tSjY,973312
scipy/interpolate/_fitpack.cp311-win_amd64.dll.a,sha256=Wn25RQrHuRaevoyATPmfFgR9aEzU_V9A9Z4Xi5cvfRU,1560
scipy/interpolate/_fitpack.cp311-win_amd64.pyd,sha256=61v1pGlbkQnMUWYwN-9owakO1QNlO2T0oXK-21CNJnY,420864
scipy/interpolate/_fitpack2.py,sha256=4jX9mcAiwvC2hFwcue03qBqwK_2siV5iiS1M6KDvlnM,92122
scipy/interpolate/_fitpack_impl.py,sha256=qk7lXA0getZg04CyTt2HnMMVoLVfRE0Jy_54RJ_k5Aw,29483
scipy/interpolate/_fitpack_py.py,sha256=Wf3EYgE1kZLvCe_svAK9UdGkdsLgr34oYJU2vz_t3aw,33055
scipy/interpolate/_fitpack_repro.py,sha256=b9bk-o5dDcbFh2BrgK61UyK_JwPFMGb3o-hkZfjk1ug,37702
scipy/interpolate/_interpnd.cp311-win_amd64.dll.a,sha256=CmuDwsPZ4R5YLlVpJtEZKI-Vyl3G2Wxt-qX-7CBYEZE,1568
scipy/interpolate/_interpnd.cp311-win_amd64.pyd,sha256=4P3OV6EkePM8rK0czL1tPpqun08PMaprkGAZxxsyFf0,411136
scipy/interpolate/_interpolate.py,sha256=NmqQa3c-vqOUjDhVJ_J5VQ6bVMODMRjB6eh3p1ibebI,81904
scipy/interpolate/_ndbspline.py,sha256=KOJEzOtnANboiQCSy1T-CWokP9u8ljpiRo48SX3Zvb0,15320
scipy/interpolate/_ndgriddata.py,sha256=CCIi5gZazNX02XKCYLmCiuLf4FjdJzvliPnuJUWaPNY,12425
scipy/interpolate/_pade.py,sha256=Phydd4f5oVM_-B3_iwe7H3t66FoCjbfsK_TagWLz9Jo,1894
scipy/interpolate/_polyint.py,sha256=WvqlFSMZvbiNbOxZgnz53M_N37KnfF37kzQuYhYq30M,37327
scipy/interpolate/_ppoly.cp311-win_amd64.dll.a,sha256=5FcwZFhCb81jX0Q1wqOMGVMpZvNHpU4TPHalo7wyDqE,1532
scipy/interpolate/_ppoly.cp311-win_amd64.pyd,sha256=Nwvx149U7NL4iog9-dKSF7HiC4fVaVZvdIUNjb0cBf0,441856
scipy/interpolate/_rbf.py,sha256=U7QwA94uCzHjxnFFYOApzqHJ6J6LoncTLs4cJEi_Mng,11964
scipy/interpolate/_rbfinterp.py,sha256=tdgC9U5X78-tCOVFB5ZmCKRs3ouCBqzVaNAUtP9lPbE,20273
scipy/interpolate/_rbfinterp_pythran.cp311-win_amd64.dll.a,sha256=MGvIBTAzwvljv1bWZh6xEMQQC5lIB3Rb4aRLu1vQepI,1676
scipy/interpolate/_rbfinterp_pythran.cp311-win_amd64.pyd,sha256=VvPCxW1Y4U_A_rszyl7kyMuQ_0lvzM8HiVVwH2MTUms,1165312
scipy/interpolate/_rgi.py,sha256=H8Mhx3RQs-Dvddd8NyUtiqWZTMebo_WMYfqdiPGP4tY,31760
scipy/interpolate/_rgi_cython.cp311-win_amd64.dll.a,sha256=2DyCsfmvPD8RAGRtcZ6rDGORzQPCNhS48266veh1iro,1592
scipy/interpolate/_rgi_cython.cp311-win_amd64.pyd,sha256=RnrmGJ4P75H-N1deLS6-HYTBZUhf53MOtNQyTs4DWmI,273408
scipy/interpolate/dfitpack.py,sha256=d50R9_Wj-obhxOGvFnmmhQP0bVhWgmW6xQNloP_D-XA,959
scipy/interpolate/fitpack.py,sha256=xFDtIx-OMu6QIsF2S3x4nEF7ZzT2c35xGeQBWDJb-Hg,733
scipy/interpolate/fitpack2.py,sha256=52AzGRbO-L23DStEBL_litxtMNILQMWLPx9fmQtyTXo,846
scipy/interpolate/interpnd.py,sha256=SG-KtPOIcge47jalfdsaZ_WtqdDc3mY8UHsQjxQv3k8,708
scipy/interpolate/interpolate.py,sha256=0MvjiPJfzyQO9_FExxZnTEq-ewR6LjiEtFF6M5RCb_U,784
scipy/interpolate/ndgriddata.py,sha256=QNqLbK1fng-TjM82puP068Stzq-TmFOSkb4mXopUJXg,659
scipy/interpolate/polyint.py,sha256=zfIlcTch3ZLhbT34EqhsfsPOi8h2X8lbnIn6u4EMgQI,696
scipy/interpolate/rbf.py,sha256=Y7ARd1hhej9QRSWR6DuRw4o_s0Tavm5DXYIU5DOdpRM,537
scipy/interpolate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/interpolate/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_bary_rational.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_bsplines.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_fitpack2.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_gil.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_interpnd.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_interpolate.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_ndgriddata.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_pade.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_polyint.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_rbf.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_rbfinterp.cpython-311.pyc,,
scipy/interpolate/tests/__pycache__/test_rgi.cpython-311.pyc,,
scipy/interpolate/tests/data/bug-1310.npz,sha256=jWgDwLOY8nBMI28dG56OXt4GvRZaCrsPIoKBq71FWuk,2648
scipy/interpolate/tests/data/estimate_gradients_hang.npy,sha256=QGwQhXQX_16pjYzSiUXJ0OT1wk-SpIrQ6Pq5Vb8kd_E,35680
scipy/interpolate/tests/data/gcvspl.npz,sha256=A86BVabLoMG_CiRBoQwigZH5Ft7DbLggcjQpgRKWu6g,3138
scipy/interpolate/tests/test_bary_rational.py,sha256=F9pYT14CaQmd3NvP5oZ3-Dcc-BuIqPhUetLn8mLhmL8,15816
scipy/interpolate/tests/test_bsplines.py,sha256=c7SbrPDbN_t-TLFHlRoO5-n34yp5wPrxOsYJN0NN9GQ,131959
scipy/interpolate/tests/test_fitpack.py,sha256=veYpMQsjwJTIQJUnBtgvQurX1yngdoaQgzPf9v8pvl0,17108
scipy/interpolate/tests/test_fitpack2.py,sha256=UCG3kFyaBMNQJHMBiUvfJ2ZLq26DFaHg9nAv4l2BkPM,61212
scipy/interpolate/tests/test_gil.py,sha256=yUdV9r0g-Af6jbmHtrgtYp3eVaE970RjL1MDqCPoID0,1895
scipy/interpolate/tests/test_interpnd.py,sha256=EJ1vc4HlH0vjW6hmN4k7kibpX1en_4wfeEeCAtt8RPY,15985
scipy/interpolate/tests/test_interpolate.py,sha256=hvg03u6yTusu5Fc2mafsVN5wZ6xKcaxuwiehLzW14N4,100363
scipy/interpolate/tests/test_ndgriddata.py,sha256=HFR7NlAVXd-aK0Oc5uOryyCCQVpSVknYoyqha527zTI,11333
scipy/interpolate/tests/test_pade.py,sha256=Ufu3_W5AQc749WLTT7ogiW_Vi0mYfz5IAQYV7Kjb6bE,3975
scipy/interpolate/tests/test_polyint.py,sha256=zuIU5G4V18snGsQCMNIKosZsphhKnt0fUI846NMtYEg,38268
scipy/interpolate/tests/test_rbf.py,sha256=IyX3UZCMF3f0yxCDp9XZJW1ntOb17sNOAKIk-fZpilU,7267
scipy/interpolate/tests/test_rbfinterp.py,sha256=S_IdiAWjWt-wTYHYspwyHncGYWZye-td7oxuzO2vbM0,19628
scipy/interpolate/tests/test_rgi.py,sha256=cGfA5CjXL8VJ4Wf_RsHRmcGkC51w2WishLIiTZfOaVo,47427
scipy/io/__init__.py,sha256=3ETOeDKr2QQL-Ty2qrrzxAzqX09sfJzsKaPm5r8tZyA,2851
scipy/io/__pycache__/__init__.cpython-311.pyc,,
scipy/io/__pycache__/_fortran.cpython-311.pyc,,
scipy/io/__pycache__/_idl.cpython-311.pyc,,
scipy/io/__pycache__/_mmio.cpython-311.pyc,,
scipy/io/__pycache__/_netcdf.cpython-311.pyc,,
scipy/io/__pycache__/harwell_boeing.cpython-311.pyc,,
scipy/io/__pycache__/idl.cpython-311.pyc,,
scipy/io/__pycache__/mmio.cpython-311.pyc,,
scipy/io/__pycache__/netcdf.cpython-311.pyc,,
scipy/io/__pycache__/wavfile.cpython-311.pyc,,
scipy/io/_fast_matrix_market/__init__.py,sha256=hZgRTdTEL5eF0wOCR72g4qAwm6K8-7-YyWyQ5zNNoOs,17721
scipy/io/_fast_matrix_market/__pycache__/__init__.cpython-311.pyc,,
scipy/io/_fast_matrix_market/_fmm_core.cp311-win_amd64.dll.a,sha256=_s7_oHwZouvW1_WXlfXennMilmF75WI1FuwV6kybb4Y,1568
scipy/io/_fast_matrix_market/_fmm_core.cp311-win_amd64.pyd,sha256=vUMxw9OYzAwz5lsOR8r7BSu8SNAEJlESwaXyWWc0tW0,2760704
scipy/io/_fortran.py,sha256=vx4KXq-MZsSRFnw0QMcxqKmEClqKkSfOY_nWQ4bnOHI,11247
scipy/io/_harwell_boeing/__init__.py,sha256=bPKU-59CohUohubhJBpDFqtFbCo3v3Ptl9hb-FHaFi8,171
scipy/io/_harwell_boeing/__pycache__/__init__.cpython-311.pyc,,
scipy/io/_harwell_boeing/__pycache__/_fortran_format_parser.cpython-311.pyc,,
scipy/io/_harwell_boeing/__pycache__/hb.cpython-311.pyc,,
scipy/io/_harwell_boeing/_fortran_format_parser.py,sha256=h_lG-IQ-C21vMX_cz7lCt0MtehgTVSAFs3MNyx56wpQ,9338
scipy/io/_harwell_boeing/hb.py,sha256=8IpGbfhnGgFkGoJjDHHP4t-T6UNdkWwTJr7NNDzEJ8A,20090
scipy/io/_harwell_boeing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/_harwell_boeing/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_fortran_format.cpython-311.pyc,,
scipy/io/_harwell_boeing/tests/__pycache__/test_hb.cpython-311.pyc,,
scipy/io/_harwell_boeing/tests/test_fortran_format.py,sha256=E3uHrWQdUXjdjbNpkYUZ516Ug4nPjWucK8fJ9ZBt77I,2457
scipy/io/_harwell_boeing/tests/test_hb.py,sha256=NrTo3Zbbk8-VwffX3dINDs4MenXNrKRJvitwLdZtXyY,2586
scipy/io/_idl.py,sha256=VbiGnNH066hsLaKgv820DYnQ0xV2y_q44zb6pe1cjKs,27994
scipy/io/_mmio.py,sha256=4OndoiMFXb3R1TTeyCbfUvwK12Pmm4qG3hGxEFXx4-c,33062
scipy/io/_netcdf.py,sha256=-v2N-DC-f4qnFMrC2yMQsmYXxxOhECZDWY6dnFGj14s,40317
scipy/io/_test_fortran.cp311-win_amd64.dll.a,sha256=1scsZhHSRmQCOzXLlVl3D0Ajsiu4Yw9qEJlbbDylrgc,1616
scipy/io/_test_fortran.cp311-win_amd64.pyd,sha256=S2iblkC3SZLgjUrxqL_OWTkllHQkDHSg7ekOVNBxMO4,381440
scipy/io/arff/__init__.py,sha256=3F0yvqizJT7Hq5gygpmZw2WMuy0r9aCmb6G2yNNn8-I,833
scipy/io/arff/__pycache__/__init__.cpython-311.pyc,,
scipy/io/arff/__pycache__/_arffread.cpython-311.pyc,,
scipy/io/arff/__pycache__/arffread.cpython-311.pyc,,
scipy/io/arff/_arffread.py,sha256=9Tx1iwYQH1lT4H-EiiBVEZeabUfSrAwY0WNEXDFLdcI,26625
scipy/io/arff/arffread.py,sha256=RMsdm5rayUdCfdr9psq85Bb15NxLk0z8A6nBeaCopNw,594
scipy/io/arff/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/arff/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/arff/tests/__pycache__/test_arffread.cpython-311.pyc,,
scipy/io/arff/tests/data/iris.arff,sha256=gzevjIJmmJLocLjOEnQMkZVZuIpfPJLocJdIG4RYHIk,7711
scipy/io/arff/tests/data/missing.arff,sha256=m53l1Himyx0TtVgNLucoz1go8-9BmSE70Ta9bm6dt_k,128
scipy/io/arff/tests/data/nodata.arff,sha256=6oAHQ7CUQEG8h5glXKpCaXH0UABOCEoxs5j50WYPAKU,240
scipy/io/arff/tests/data/quoted_nominal.arff,sha256=rc8IS9thME1NM9tAR-C4IUixLrSpw64YNeHkIRMS6Ng,299
scipy/io/arff/tests/data/quoted_nominal_spaces.arff,sha256=NbE6wwE0HkJDomErLPHgPGO6iF2HA1bLpmcamzx3F58,305
scipy/io/arff/tests/data/test1.arff,sha256=NV86lzIKWCwq3UNZmMGt1XK0yurcYmcp0KS9c5rKLkE,201
scipy/io/arff/tests/data/test10.arff,sha256=GbDWnG6LzjsbjYxiarYG4Ft3WlK1rvuM6w3proNFD58,199016
scipy/io/arff/tests/data/test11.arff,sha256=RFWNoWrx2LHJ0izc1bJdmGA2M2L8NT58MwKQN2B-3AI,252
scipy/io/arff/tests/data/test2.arff,sha256=zk5hNFTEcphFXkon2At_FD9ia_lAyzOcSFj4xnJTVPs,315
scipy/io/arff/tests/data/test3.arff,sha256=R2zwCzapweGqylmLD-LXx7GAS957NNJ8emM48hLTwEY,78
scipy/io/arff/tests/data/test4.arff,sha256=KtyokEnupbYZVi-fR7GJtr-hHefP54xgV2l26Nz3YZU,249
scipy/io/arff/tests/data/test5.arff,sha256=_1-QhpmflJKp1polaKwjkeJZsHeho7RzM3_ic2JJykA,391
scipy/io/arff/tests/data/test6.arff,sha256=WOCPN8QRdxCUSfzs-Y-OlJKiUmgLKuDrfy6wsWIFCJU,207
scipy/io/arff/tests/data/test7.arff,sha256=AQdPXYEKEhb10Odd_SDrPcv_gHNR0ymFKnD_91zjFc8,573
scipy/io/arff/tests/data/test8.arff,sha256=YlCbztVn-7x4w41HLIP852BqkKvWBrO9N8Nb0LSR0ZM,440
scipy/io/arff/tests/data/test9.arff,sha256=X0fH-_mz5ScbEvPJV7LJH-7Kbr2RTOlamf2_x18ydY0,324
scipy/io/arff/tests/test_arffread.py,sha256=E7OS_dIWm_QxiqnoOWZ64--ZY9_yoRYQ_lfSFu1TIrA,13579
scipy/io/harwell_boeing.py,sha256=uZ7NZBLRwQ39GlELGYby_JsIHjMDp1NGbc7cjWq6OFs,555
scipy/io/idl.py,sha256=FgICucFl-tR94QRdtx099cFD8YEbGJ_fH9A1cT5qRGg,521
scipy/io/matlab/__init__.py,sha256=Z4ooxkPutnZg9AgjTWcQukjRVHWH_9JkyWFKS0k4_-g,2220
scipy/io/matlab/__pycache__/__init__.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_byteordercodes.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio4.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio5.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_mio5_params.cpython-311.pyc,,
scipy/io/matlab/__pycache__/_miobase.cpython-311.pyc,,
scipy/io/matlab/__pycache__/byteordercodes.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio4.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio5.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio5_params.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio5_utils.cpython-311.pyc,,
scipy/io/matlab/__pycache__/mio_utils.cpython-311.pyc,,
scipy/io/matlab/__pycache__/miobase.cpython-311.pyc,,
scipy/io/matlab/__pycache__/streams.cpython-311.pyc,,
scipy/io/matlab/_byteordercodes.py,sha256=5QL5iQVv3Uj_dki1NOs1m5f1yiu2VNF83kXNNYOE_58,2058
scipy/io/matlab/_mio.py,sha256=0qw6uZLXFWFDI9ZfRVNiUT4Bda9ggzcAD0K2WLkfanI,13959
scipy/io/matlab/_mio4.py,sha256=28UPTFY51MGcAh0c6-bjLKAFScgmqYOMiNh4Jw2298w,21625
scipy/io/matlab/_mio5.py,sha256=fpyd7Vl3i5F_D8OPIWK3Zj49xxUwG8jC13qQ8UA7nsM,34532
scipy/io/matlab/_mio5_params.py,sha256=f5HyjzbAwkN4zNRwn0N_n01LadTbsmUFGWjflumbxeU,8482
scipy/io/matlab/_mio5_utils.cp311-win_amd64.dll.a,sha256=YzsEFROsXT-vkf708-_jPDeUj31RKkCr9rpyLUcPjZY,1592
scipy/io/matlab/_mio5_utils.cp311-win_amd64.pyd,sha256=f-fzDWojEuBUucM_Pr8SF0i6zMgt2wG_8r38hu1gYFA,210432
scipy/io/matlab/_mio_utils.cp311-win_amd64.dll.a,sha256=pKgNxrtYufHLzMinzajy1Gcef2W9pamKYCro5-jUTkA,1580
scipy/io/matlab/_mio_utils.cp311-win_amd64.pyd,sha256=c5CbC7-tXRaBgNN7p1z8sd3XsB3iWg8fnXG2wyvUbiY,58368
scipy/io/matlab/_miobase.py,sha256=a8j5xBUfhS2hwqOyQemrtaADMq8sALyKDeznCaw7TGM,13436
scipy/io/matlab/_streams.cp311-win_amd64.dll.a,sha256=_PPVaXD8QOaocxjMHS1ZAqvm9wZ-7HqRrY0dsyP6Gxw,1560
scipy/io/matlab/_streams.cp311-win_amd64.pyd,sha256=FQfuxS5Al2FpNF1HBdrVu_q2L0nmiK6OQLCyTlG9Rww,113664
scipy/io/matlab/byteordercodes.py,sha256=iyqwM-3YIUCH9LJdOEbvMJ7n2gu6nxKdyb0zspXeyA4,545
scipy/io/matlab/mio.py,sha256=Rd-5E8FJ__4ylQfqBLsiZXuHdOs_kpqW1gu6U8uuEjA,555
scipy/io/matlab/mio4.py,sha256=-r3TPyoOQL2JFpfg_Tkkq1TLGVwU5puFeSfpDfP2_qc,525
scipy/io/matlab/mio5.py,sha256=LDYpi8yS5Sh_1n-U665ILxjhv2k4aEdepb4jv6pdIUc,657
scipy/io/matlab/mio5_params.py,sha256=k_DuRuxmwMBXDdKrrpa2gDmU0ZLudzxyKJhmiDZ32fg,611
scipy/io/matlab/mio5_utils.py,sha256=zAGGUPRweBSnK2VclfLePonc3N63WwH7q4rta4VFBzM,537
scipy/io/matlab/mio_utils.py,sha256=3YjORgt2MmmZvxXLnk680YX-3cs7YVvKxRyMqUfHo4M,535
scipy/io/matlab/miobase.py,sha256=ucyFrqpuWm2GNepC3PB9Zg0xs2bGVqvYPscphlxVHbQ,581
scipy/io/matlab/streams.py,sha256=QjbFVhqfk3IR1ow5SPpE37Be47jJr5169sNTC1hhpmY,529
scipy/io/matlab/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/matlab/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_byteordercodes.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio5_utils.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_funcs.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_mio_utils.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_miobase.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_pathological.cpython-311.pyc,,
scipy/io/matlab/tests/__pycache__/test_streams.cpython-311.pyc,,
scipy/io/matlab/tests/data/bad_miuint32.mat,sha256=CVkYHp_U4jxYKRRHSuZ5fREop4tJjnZcQ02DKfObkRA,272
scipy/io/matlab/tests/data/bad_miutf8_array_name.mat,sha256=V-jfVMkYyy8qRGcOIsNGcoO0GCgTxchrsQUBGBnfWHE,208
scipy/io/matlab/tests/data/big_endian.mat,sha256=2ttpiaH2B6nmHnq-gsFeMvZ2ZSLOlpzt0IJiqBTcc8M,273
scipy/io/matlab/tests/data/broken_utf8.mat,sha256=nm8aotRl6NIxlM3IgPegKR3EeevYZoJCrYpV4Sa1T5I,216
scipy/io/matlab/tests/data/corrupted_zlib_checksum.mat,sha256=X4dvE7K9DmGEF3D6I-48hC86W41jB54H7bD8KTXjtYA,276
scipy/io/matlab/tests/data/corrupted_zlib_data.mat,sha256=DfE1YBH-pYw-dAaEeKA6wZcyKeo9GlEfrzZtql-fO_w,3451
scipy/io/matlab/tests/data/debigged_m4.mat,sha256=8QbD-LzoYbKSfOYPRRw-oelDJscwufYp5cqLfZ1hB0c,1024
scipy/io/matlab/tests/data/japanese_utf8.txt,sha256=rgxiBH7xmEKF91ZkB3oMLrqABBXINEMHPXDKdZXNBEY,270
scipy/io/matlab/tests/data/little_endian.mat,sha256=FQP_2MNod-FFF-JefN7ZxovQ6QLCdHQ0DPL_qBCP44Y,265
scipy/io/matlab/tests/data/logical_sparse.mat,sha256=qujUUpYewaNsFKAwGpYS05z7kdUv9TQZTHV5_lWhRrs,208
scipy/io/matlab/tests/data/malformed1.mat,sha256=DTuTr1-IzpLMBf8u5DPb3HXmw9xJo1aWfayA5S_3zUI,2208
scipy/io/matlab/tests/data/miuint32_for_miint32.mat,sha256=romrBP_BS46Sl2-pKWsUnxYDad2wehyjq4wwLaVqums,272
scipy/io/matlab/tests/data/miutf8_array_name.mat,sha256=Vo8JptFr-Kg2f2cEoDg8LtELSjVNyccdJY74WP_kqtc,208
scipy/io/matlab/tests/data/nasty_duplicate_fieldnames.mat,sha256=bvdmj6zDDUIpOfIP8J4Klo107RYCDd5VK5gtOYx3GsU,8168
scipy/io/matlab/tests/data/one_by_zero_char.mat,sha256=Z3QdZjTlOojjUpS0cfBP4XfNQI3GTjqU0n_pnAzgQhU,184
scipy/io/matlab/tests/data/parabola.mat,sha256=ENWuWX_uwo4Av16dIGOwnbMReAMrShDhalkq8QUI8Rg,729
scipy/io/matlab/tests/data/single_empty_string.mat,sha256=4uTmX0oydTjmtnhxqi9SyPWCG2I24gj_5LarS80bPik,171
scipy/io/matlab/tests/data/some_functions.mat,sha256=JA736oG3s8PPdKhdsYK-BndLUsGrJCJAIRBseSIEZtM,1397
scipy/io/matlab/tests/data/sqr.mat,sha256=3DtGl_V4wABKCDQ0P3He5qfOzpUTC-mINdK73MKS7AM,679
scipy/io/matlab/tests/data/test3dmatrix_6.1_SOL2.mat,sha256=-odiBIQAbOLERg0Vg682QHGfs7C8MaA_gY77OWR8x78,232
scipy/io/matlab/tests/data/test3dmatrix_6.5.1_GLNX86.mat,sha256=G5siwvZ-7Uv5KJ6h7AA3OHL6eiFsd8Lnjx4IcoByzCU,232
scipy/io/matlab/tests/data/test3dmatrix_7.1_GLNX86.mat,sha256=EVj1wPnoyWGIdTpkSj3YAwqzTAm27eqZNxCaJAs3pwU,213
scipy/io/matlab/tests/data/test3dmatrix_7.4_GLNX86.mat,sha256=S_Sd3sxorDd8tZ5CxD5_J8vXbfcksLWzhUQY5b82L9g,213
scipy/io/matlab/tests/data/test_empty_struct.mat,sha256=WoC7g7TyXqNr2T0d5xE3IUq5PRzatE0mxXjqoHX5Xec,173
scipy/io/matlab/tests/data/test_mat4_le_floats.mat,sha256=2xvn3Cg4039shJl62T-bH-VeVP_bKtwdqvGfIxv8FJ4,38
scipy/io/matlab/tests/data/test_skip_variable.mat,sha256=pJLVpdrdEb-9SMZxaDu-uryShlIi90l5LfXhvpVipJ0,20225
scipy/io/matlab/tests/data/testbool_8_WIN64.mat,sha256=_xBw_2oZA7u9Xs6GJItUpSIEV4jVdfdcwzmLNFWM6ow,185
scipy/io/matlab/tests/data/testcell_6.1_SOL2.mat,sha256=OWOBzNpWTyAHIcZABRytVMcABiRYgEoMyF9gDaIkFe4,536
scipy/io/matlab/tests/data/testcell_6.5.1_GLNX86.mat,sha256=7111TN_sh1uMHmYx-bjd_v9uaAnWhJMhrQFAtAw6Nvk,536
scipy/io/matlab/tests/data/testcell_7.1_GLNX86.mat,sha256=62p6LRW6PbM-Y16aUeGVhclTVqS5IxPUtsohe7MjrYo,283
scipy/io/matlab/tests/data/testcell_7.4_GLNX86.mat,sha256=NkTA8UW98hIQ0t5hGx_leG-MzNroDelYwqx8MPnO63Q,283
scipy/io/matlab/tests/data/testcellnest_6.1_SOL2.mat,sha256=AeNaog8HUDCVrIuGICAXYu9SGDsvV6qeGjgvWHrVQho,568
scipy/io/matlab/tests/data/testcellnest_6.5.1_GLNX86.mat,sha256=Gl4QA0yYwGxjiajjgWS939WVAM-W2ahNIm9wwMaT5oc,568
scipy/io/matlab/tests/data/testcellnest_7.1_GLNX86.mat,sha256=CUGtkwIU9CBa0Slx13mbaM67_ec0p-unZdu8Z4YYM3c,228
scipy/io/matlab/tests/data/testcellnest_7.4_GLNX86.mat,sha256=TeTk5yjl5j_bcnmIkpzuYHxGGQXNu-rK6xOsN4t6lX8,228
scipy/io/matlab/tests/data/testcomplex_4.2c_SOL2.mat,sha256=WOwauWInSVUFBuOJ1Bo3spmUQ3UWUIlsIe4tYGlrU7o,176
scipy/io/matlab/tests/data/testcomplex_6.1_SOL2.mat,sha256=GpAEccizI8WvlrBPdvlKUv6uKbZOo_cjUK3WVVb2lo4,352
scipy/io/matlab/tests/data/testcomplex_6.5.1_GLNX86.mat,sha256=3MEbf0zJdQGAO7x-pzFCup2QptfYJHQG59z0vVOdxl4,352
scipy/io/matlab/tests/data/testcomplex_7.1_GLNX86.mat,sha256=VNHV2AIEkvPuhae1kKIqt5t8AMgUyr0L_CAp-ykLxt4,247
scipy/io/matlab/tests/data/testcomplex_7.4_GLNX86.mat,sha256=8rWGf5bqY7_2mcd5w5gTYgMkXVePlLL8qT7lh8kApn0,247
scipy/io/matlab/tests/data/testdouble_4.2c_SOL2.mat,sha256=MzT7OYPEUXHYNPBrVkyKEaG5Cas2aOA0xvrO7l4YTrQ,103
scipy/io/matlab/tests/data/testdouble_6.1_SOL2.mat,sha256=DpB-mVKx1gsjl-3IbxfxHNuzU5dnuku-MDQCA8kALVI,272
scipy/io/matlab/tests/data/testdouble_6.5.1_GLNX86.mat,sha256=4hY5VEubavNEv5KvcqQnd7MWWvFUzHXXpYIqUuUt-50,272
scipy/io/matlab/tests/data/testdouble_7.1_GLNX86.mat,sha256=N2QOOIXPyy0zPZZ_qY7xIDaodMGrTq3oXNBEHZEscw0,232
scipy/io/matlab/tests/data/testdouble_7.4_GLNX86.mat,sha256=TrkJ4Xx_dC9YrPdewlsOvYs_xag7gT3cN4HkDsJmT8I,232
scipy/io/matlab/tests/data/testemptycell_5.3_SOL2.mat,sha256=g96Vh9FpNhkiWKsRm4U6KqeKd1hNAEyYSD7IVzdzwsU,472
scipy/io/matlab/tests/data/testemptycell_6.5.1_GLNX86.mat,sha256=2Zw-cMv-Mjbs2HkSl0ubmh_htFUEpkn7XVHG8iM32o0,472
scipy/io/matlab/tests/data/testemptycell_7.1_GLNX86.mat,sha256=t5Ar8EgjZ7fkTUHIVpdXg-yYWo_MBaigMDJUGWEIrmU,218
scipy/io/matlab/tests/data/testemptycell_7.4_GLNX86.mat,sha256=5PPvfOoL-_Q5ou_2nIzIrHgeaOZGFXGxAFdYzCQuwEQ,218
scipy/io/matlab/tests/data/testfunc_7.4_GLNX86.mat,sha256=ScTKftENe78imbMc0I5ouBlIMcEEmZgu8HVKWAMNr58,381
scipy/io/matlab/tests/data/testhdf5_7.4_GLNX86.mat,sha256=ZoVbGk38_MCppZ0LRr6OE07HL8ZB4rHXgMj9LwUBgGg,4168
scipy/io/matlab/tests/data/testmatrix_4.2c_SOL2.mat,sha256=14YMiKAN9JCPTqSDXxa58BK6Un7EM4hEoSGAUuwKWGQ,151
scipy/io/matlab/tests/data/testmatrix_6.1_SOL2.mat,sha256=ZdjNbcIE75V5Aht5EVBvJX26aabvNqbUH0Q9VBnxBS4,216
scipy/io/matlab/tests/data/testmatrix_6.5.1_GLNX86.mat,sha256=OB82QgB6SwtsxT4t453OVSj-B777XrHGEGOMgMD1XGc,216
scipy/io/matlab/tests/data/testmatrix_7.1_GLNX86.mat,sha256=-TYB0kREY7i7gt5x15fOYjXi410pXuDWUFxPYuMwywI,193
scipy/io/matlab/tests/data/testmatrix_7.4_GLNX86.mat,sha256=l9psDc5K1bpxNeuFlyYIYauswLnOB6dTX6-jvelW0kU,193
scipy/io/matlab/tests/data/testminus_4.2c_SOL2.mat,sha256=2914WYQajPc9-Guy3jDOLU3YkuE4OXC_63FUSDzJzX0,38
scipy/io/matlab/tests/data/testminus_6.1_SOL2.mat,sha256=2X2fZKomz0ktBvibj7jvHbEvt2HRA8D6hN9qA1IDicw,200
scipy/io/matlab/tests/data/testminus_6.5.1_GLNX86.mat,sha256=i364SgUCLSYRjQsyygvY1ArjEaO5uLip3HyU-R7zaLo,200
scipy/io/matlab/tests/data/testminus_7.1_GLNX86.mat,sha256=gtYNC9_TciYdq8X9IwyGEjiw2f1uCVTGgiOPFOiQbJc,184
scipy/io/matlab/tests/data/testminus_7.4_GLNX86.mat,sha256=eXcoTM8vKuh4tQnl92lwdDaqssGB6G9boSHh3FOCkng,184
scipy/io/matlab/tests/data/testmulti_4.2c_SOL2.mat,sha256=Zhyu2KCsseSJ5NARdS00uwddCs4wmjcWNP2LJFns2-Q,240
scipy/io/matlab/tests/data/testmulti_7.1_GLNX86.mat,sha256=KI3H58BVj6k6MFsj8icSbjy_0Z-jOesWN5cafStLPG8,276
scipy/io/matlab/tests/data/testmulti_7.4_GLNX86.mat,sha256=Yr4YKCP27yMWlK5UOK3BAEOAyMr-m0yYGcj8v1tCx-I,276
scipy/io/matlab/tests/data/testobject_6.1_SOL2.mat,sha256=kzLxy_1o1HclPXWyA-SX5gl6LsG1ioHuN4eS6x5iZio,800
scipy/io/matlab/tests/data/testobject_6.5.1_GLNX86.mat,sha256=dq_6_n0v7cUz9YziXn-gZFNc9xYtNxZ8exTsziWIM7s,672
scipy/io/matlab/tests/data/testobject_7.1_GLNX86.mat,sha256=3z-boFw0SC5142YPOLo2JqdusPItVzjCFMhXAQNaQUQ,306
scipy/io/matlab/tests/data/testobject_7.4_GLNX86.mat,sha256=5OwLTMgCBlxsDfiEUzlVjqcSbVQG-X5mIw5JfW3wQXA,306
scipy/io/matlab/tests/data/testonechar_4.2c_SOL2.mat,sha256=BCvppGhO19-j-vxAvbdsORIiyuJqzCuQog9Ao8V1lvA,40
scipy/io/matlab/tests/data/testonechar_6.1_SOL2.mat,sha256=ThppTHGJFrUfal5tewS70DL00dSwk1otazuVdJrTioE,200
scipy/io/matlab/tests/data/testonechar_6.5.1_GLNX86.mat,sha256=SBfN6e7Vz1rAdi8HLguYXcHUHk1viaXTYccdEyhhob4,200
scipy/io/matlab/tests/data/testonechar_7.1_GLNX86.mat,sha256=m8W9GqvflfAsizkhgAfT0lLcxuegZIWCLNuHVX69Jac,184
scipy/io/matlab/tests/data/testonechar_7.4_GLNX86.mat,sha256=t9ObKZOLy3vufnER8TlvQcUkd_wmXbJSdQoG4f3rVKY,184
scipy/io/matlab/tests/data/testscalarcell_7.4_GLNX86.mat,sha256=5LX9sLH7Y6h_N_a1XRN2GuMgp_P7ECpPsXGDOypAJg0,194
scipy/io/matlab/tests/data/testsimplecell.mat,sha256=Aoeh0PX2yiLDTwkxMEyZ_CNX2mJHZvyfuFJl817pA1c,220
scipy/io/matlab/tests/data/testsparse_4.2c_SOL2.mat,sha256=dFUcB1gunfWqexgR4YDZ_Ec0w0HffM1DUE1C5PVfDDc,223
scipy/io/matlab/tests/data/testsparse_6.1_SOL2.mat,sha256=9Sgd_SPkGNim7ZL0xgD71qml3DK0yDHYC7VSNLNQEXA,280
scipy/io/matlab/tests/data/testsparse_6.5.1_GLNX86.mat,sha256=jp1ILNxLyV6XmCCGxAz529XoZ9dhCqGEO-ExPH70_Pg,328
scipy/io/matlab/tests/data/testsparse_7.1_GLNX86.mat,sha256=k8QuQ_4Zu7FWTzHjRnHCVZ9Yu5vwNP0WyNzu6TuiY-4,229
scipy/io/matlab/tests/data/testsparse_7.4_GLNX86.mat,sha256=QbZOCqIvnaK0XOH3kaSXBe-m_1_Rb33psq8E-WMSBTU,229
scipy/io/matlab/tests/data/testsparsecomplex_4.2c_SOL2.mat,sha256=QMVoBXVyl9RBGvAjLoiW85kAXYJ-hHprUMegEG69A5w,294
scipy/io/matlab/tests/data/testsparsecomplex_6.1_SOL2.mat,sha256=WfEroAT5YF4HGAKq3jTJxlFrKaTCh3rwlSlKu__VjwA,304
scipy/io/matlab/tests/data/testsparsecomplex_6.5.1_GLNX86.mat,sha256=e0s6cyoKJeYMArdceHpnKDvtCVcw7XuB44OBDHpoa6U,400
scipy/io/matlab/tests/data/testsparsecomplex_7.1_GLNX86.mat,sha256=kgHcuq-deI2y8hfkGwlMOkW7lntexdPHfuz0ar6b3jo,241
scipy/io/matlab/tests/data/testsparsecomplex_7.4_GLNX86.mat,sha256=rYCaWNLXK7f_jjMc6_UvZz6ZDuMCuVRmJV5RyeXiDm8,241
scipy/io/matlab/tests/data/testsparsefloat_7.4_GLNX86.mat,sha256=hnNV6GZazEeqTXuA9vcOUo4xam_UnKRYGYH9PUGTLv8,219
scipy/io/matlab/tests/data/teststring_4.2c_SOL2.mat,sha256=cAhec51DlqIYfDXXGaumOE3Hqb3cFWM1UsUK3K_lDP8,375
scipy/io/matlab/tests/data/teststring_6.1_SOL2.mat,sha256=ciFzNGMO7gjYecony-E8vtOwBY4vXIUhyug6Euaz3Kg,288
scipy/io/matlab/tests/data/teststring_6.5.1_GLNX86.mat,sha256=yrJrpLiwLvU_LI1D6rw1Pk1qJK1YlC7Cmw7lwyJVLtw,288
scipy/io/matlab/tests/data/teststring_7.1_GLNX86.mat,sha256=zo7sh-8dMpGqhoNxLEnfz3Oc7RonxiY5j0B3lxk0e8o,224
scipy/io/matlab/tests/data/teststring_7.4_GLNX86.mat,sha256=igL_CvtAcNEa1nxunDjQZY5wS0rJOlzsUkBiDreJssk,224
scipy/io/matlab/tests/data/teststringarray_4.2c_SOL2.mat,sha256=pRldk-R0ig1k3ouvaR9oVtBwZsQcDW_b4RBEDYu1-Vk,156
scipy/io/matlab/tests/data/teststringarray_6.1_SOL2.mat,sha256=B9IdaSsyb0wxjyYyHOj_GDO0laAeWDEJhoEhC9xdm1E,232
scipy/io/matlab/tests/data/teststringarray_6.5.1_GLNX86.mat,sha256=t4tKGJg2NEg_Ar5MkOjCoQb2hVL8Q_Jdh9FF4TPL_4g,232
scipy/io/matlab/tests/data/teststringarray_7.1_GLNX86.mat,sha256=lpYkBZX8K-c4FO5z0P9DMfYc7Y-yzyg11J6m-19uYTU,203
scipy/io/matlab/tests/data/teststringarray_7.4_GLNX86.mat,sha256=lG-c7U-5Bo8j8xZLpd0JAsMYwewT6cAw4eJCZH5xf6E,203
scipy/io/matlab/tests/data/teststruct_6.1_SOL2.mat,sha256=3GJbA4O7LP57J6IYzmJqTPeSJrEaiNSk-rg7h0ANR1w,608
scipy/io/matlab/tests/data/teststruct_6.5.1_GLNX86.mat,sha256=fRbqAnzTeOU3dTQx7O24MfMVFr6pM5u594FRrPPkYJE,552
scipy/io/matlab/tests/data/teststruct_7.1_GLNX86.mat,sha256=mCtI_Yot08NazvWHvehOZbTV4bW_I4-D5jBgJ6T9EbI,314
scipy/io/matlab/tests/data/teststruct_7.4_GLNX86.mat,sha256=52qaF4HRCtPl1jE6ljbkEl2mofZVAPpmBxrm-J5OTTI,314
scipy/io/matlab/tests/data/teststructarr_6.1_SOL2.mat,sha256=vneCpWBwApBGfeKzdZcybyajxjR-ZYf64j0l08_hU84,528
scipy/io/matlab/tests/data/teststructarr_6.5.1_GLNX86.mat,sha256=gqhRpSfNNB5SR9sCp-wWrvokr5VV_heGnvco6dmfOvY,472
scipy/io/matlab/tests/data/teststructarr_7.1_GLNX86.mat,sha256=6VDU0mtTBEG0bBHqKP1p8xq846eMhSZ_WvBZv8MzE7M,246
scipy/io/matlab/tests/data/teststructarr_7.4_GLNX86.mat,sha256=ejtyxeeX_W1a2rNrEUUiG9txPW8_UtSgt8IaDOxE2pg,246
scipy/io/matlab/tests/data/teststructnest_6.1_SOL2.mat,sha256=sbi0wUwOrbU-gBq3lyDwhAbvchdtOJkflOR_MU7uGKA,496
scipy/io/matlab/tests/data/teststructnest_6.5.1_GLNX86.mat,sha256=uTkKtrYBTuz4kICVisEaG7V5C2nJDKjy92mPDswTLPE,416
scipy/io/matlab/tests/data/teststructnest_7.1_GLNX86.mat,sha256=o4F2jOhYyNpJCo-BMg6v_ITZQvjenXfXHLq94e7iwRo,252
scipy/io/matlab/tests/data/teststructnest_7.4_GLNX86.mat,sha256=CNXO12O6tedEuMG0jNma4qfbTgCswAbHwh49a3uE3Yk,252
scipy/io/matlab/tests/data/testunicode_7.1_GLNX86.mat,sha256=KV97FCW-1XZiXrwXJoZPbgyAht79oIFHa917W1KFLwE,357
scipy/io/matlab/tests/data/testunicode_7.4_GLNX86.mat,sha256=9-8xzACZleBkMjZnbr8t4Ncs9B6mbzrONDblPnteBPU,357
scipy/io/matlab/tests/data/testvec_4_GLNX86.mat,sha256=GQzR3mBVS266_NBfrRC9X0dLgmeu8Jl4r4ZYMOrn1V0,93
scipy/io/matlab/tests/test_byteordercodes.py,sha256=YKPAsE36MRPDi3Jsh2QrinvLOEvDPmsBylkwUzqoDTg,967
scipy/io/matlab/tests/test_mio.py,sha256=sPp-QHwSAUeOYxyEq_HRxcr8W_TIBs7YTUaM_XoQ5GA,47578
scipy/io/matlab/tests/test_mio5_utils.py,sha256=Z1Byr0AJMkdlidOETqIsAJdCZCx0TGqR4OyFI4SDBaY,5568
scipy/io/matlab/tests/test_mio_funcs.py,sha256=JoLN42aZLd-f-kju-kTgRFQjQmEy4VtJlKgJ5EgnVFI,1443
scipy/io/matlab/tests/test_mio_utils.py,sha256=I4_z8My_JDy6jverX4bm-GeDNnnC6jX71hprsGSnCF8,1639
scipy/io/matlab/tests/test_miobase.py,sha256=Ju2SKwhieuAs2AZnt8NhEw1DVc_BpLts4BvYn2SaaOE,1492
scipy/io/matlab/tests/test_pathological.py,sha256=8lcveXzzknQH_009kNTvrf4aAr-qgatrXPEuRZtxQ4w,1088
scipy/io/matlab/tests/test_streams.py,sha256=3T0aC_eSvutWlfNsKOL4JCUUqg9jYEhGbPgJ4ZdynpM,7638
scipy/io/mmio.py,sha256=qDHalmZoj_VZLTcaO65zAXs-1ZF69aeKoF9p1R-Mh1w,543
scipy/io/netcdf.py,sha256=YaRtHSM0jdaFB5W-Og7vU6CsgaVhEOaogTwObhuHTOA,550
scipy/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/io/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_fortran.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_idl.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_mmio.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_netcdf.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_paths.cpython-311.pyc,,
scipy/io/tests/__pycache__/test_wavfile.cpython-311.pyc,,
scipy/io/tests/data/Transparent Busy.ani,sha256=vwoK3ysYo87-TwzvjerHjFjSPIGpw83jjiMDXcHPWjA,4362
scipy/io/tests/data/array_float32_1d.sav,sha256=A_xXWkfS1sQCxP4ONezeEZvlKEXwZ1TPG2rCCFdmBNM,2628
scipy/io/tests/data/array_float32_2d.sav,sha256=qJmN94pywXznXMHzt-L6DJgaIq_FfruVKJl_LMaI8UU,3192
scipy/io/tests/data/array_float32_3d.sav,sha256=U7P6As7Nw6LdBY1pTOaW9C-O_NlXLXZwSgbT3H8Z8uk,13752
scipy/io/tests/data/array_float32_4d.sav,sha256=Tl6erEw_Zq3dwVbVyPXRWqB83u_o4wkIVFOe3wQrSro,6616
scipy/io/tests/data/array_float32_5d.sav,sha256=VmaBgCD854swYyLouDMHJf4LL6iUNgajEOQf0pUjHjg,7896
scipy/io/tests/data/array_float32_6d.sav,sha256=lb7modI0OQDweJWbDxEV2OddffKgMgq1tvCy5EK6sOU,19416
scipy/io/tests/data/array_float32_7d.sav,sha256=pqLWIoxev9sLCs9LLwxFlM4RCFwxHC4Q0dEEz578mpI,3288
scipy/io/tests/data/array_float32_8d.sav,sha256=R8A004f9XLWvF6eKMNEqIrC6PGP1vLZr9sFqawqM8ZA,13656
scipy/io/tests/data/array_float32_pointer_1d.sav,sha256=sV7qFNwHK-prG5vODa7m5HYK7HlH_lqdfsI5Y1RWDyg,2692
scipy/io/tests/data/array_float32_pointer_2d.sav,sha256=b0brvK6xQeezoRuujmEcJNw2v6bfASLM3FSY9u5dMSg,3256
scipy/io/tests/data/array_float32_pointer_3d.sav,sha256=a_Iyg1YjPBRh6B-N_n_BGIVjFje4K-EPibKV-bPbF7E,13816
scipy/io/tests/data/array_float32_pointer_4d.sav,sha256=cXrkHHlPyoYstDL_OJ15-55sZOOeDNW2OJ3KWhBv-Kk,6680
scipy/io/tests/data/array_float32_pointer_5d.sav,sha256=gRVAZ6jeqFZyIQI9JVBHed9Y0sjS-W4bLseb01rIcGs,7960
scipy/io/tests/data/array_float32_pointer_6d.sav,sha256=9yic-CQiS0YR_ow2yUA2Nix0Nb_YCKMUsIgPhgcJT1c,19480
scipy/io/tests/data/array_float32_pointer_7d.sav,sha256=Rp1s8RbW8eoEIRTqxba4opAyY0uhTuyy3YkwRlNspQU,3352
scipy/io/tests/data/array_float32_pointer_8d.sav,sha256=Wk3Dd2ClAwWprXLKZon3blY7aMvMrJqz_NXzK0J5MFY,13720
scipy/io/tests/data/example_1.nc,sha256=EkfC57dWXeljgXy5sidrJHJG12D1gmQUyPDK18WzlT4,1736
scipy/io/tests/data/example_2.nc,sha256=wywMDspJ2QT431_sJUr_5DHqG3pt9VTvDJzfR9jeWCk,272
scipy/io/tests/data/example_3_maskedvals.nc,sha256=P9N92jCJgKJo9VmNd7FeeJSvl4yUUFwBy6JpR4MeuME,1424
scipy/io/tests/data/fortran-3x3d-2i.dat,sha256=oYCXgtY6qqIqLAhoh_46ob_RVQRcV4uu333pOiLKgRM,451
scipy/io/tests/data/fortran-mixed.dat,sha256=zTi7RLEnyAat_DdC3iSEcSbyDtAu0aTKwUT-tExjasw,40
scipy/io/tests/data/fortran-sf8-11x1x10.dat,sha256=KwaOrZOAe-wRhuxvmHIK-Wr59us40MmiA9QyWtIAUaA,888
scipy/io/tests/data/fortran-sf8-15x10x22.dat,sha256=5ohvjjOUcIsGimSqDhpUUKwflyhVsfwKL5ElQe_SU0I,26408
scipy/io/tests/data/fortran-sf8-1x1x1.dat,sha256=Djmoip8zn-UcxWGUPKV5wzKOYOf7pbU5L7HaR3BYlec,16
scipy/io/tests/data/fortran-sf8-1x1x5.dat,sha256=Btgavm3w3c9md_5yFfq6Veo_5IK9KtlLF1JEPeHhZoU,48
scipy/io/tests/data/fortran-sf8-1x1x7.dat,sha256=L0r9yAEMbfMwYQytzYsS45COqaVk-o_hi6zRY3yIiO4,64
scipy/io/tests/data/fortran-sf8-1x3x5.dat,sha256=c2LTocHclwTIeaR1Pm3mVMyf5Pl_imfjIFwi4Lpv0Xs,128
scipy/io/tests/data/fortran-si4-11x1x10.dat,sha256=OesvSIGsZjpKZlZsV74PNwy0Co0KH8-3gxL9-DWoa08,448
scipy/io/tests/data/fortran-si4-15x10x22.dat,sha256=OJcKyw-GZmhHb8REXMsHDn7W5VP5bhmxgVPIAYG-Fj4,13208
scipy/io/tests/data/fortran-si4-1x1x1.dat,sha256=1Lbx01wZPCOJHwg99MBDuc6QZKdMnccxNgICt4omfFM,12
scipy/io/tests/data/fortran-si4-1x1x5.dat,sha256=L1St4yiHTA3v91JjnndYfUrdKfT1bWxckwnnrscEZXc,28
scipy/io/tests/data/fortran-si4-1x1x7.dat,sha256=Dmqt-tD1v2DiPZkghGGZ9Ss-nJGfei-3yFXPO5Acpk4,36
scipy/io/tests/data/fortran-si4-1x3x5.dat,sha256=3vl6q93m25jEcZVKD0CuKNHmhZwZKp-rv0tfHoPVP88,68
scipy/io/tests/data/invalid_pointer.sav,sha256=JmgoISXC4r5fSmI5FqyapvmzQ4qpYLf-9N7_Et1p1HQ,1280
scipy/io/tests/data/null_pointer.sav,sha256=P_3a_sU614F3InwM82jSMtWycSZkvqRn1apwd8XxbtE,2180
scipy/io/tests/data/scalar_byte.sav,sha256=dNJbcE5OVDY_wHwN_UBUtfIRd13Oqu-RBEO74g5SsBA,2076
scipy/io/tests/data/scalar_byte_descr.sav,sha256=DNTmDgDWOuzlQnrceER6YJ0NutUUwZ9tozVMBWQmuuY,2124
scipy/io/tests/data/scalar_complex32.sav,sha256=NGd-EvmFZgt8Ko5MP3T_TLwyby6yS0BXM_OW8197hpU,2076
scipy/io/tests/data/scalar_complex64.sav,sha256=gFBWtxuAajazupGFSbvlWUPDYK-JdWgZcEWih2-7IYU,2084
scipy/io/tests/data/scalar_float32.sav,sha256=EwWQw2JTwq99CHVpDAh4R20R0jWaynXABaE2aTRmXrs,2072
scipy/io/tests/data/scalar_float64.sav,sha256=iPcDlgF1t0HoabvNLWCbSiTPIa9rvVEbOGGmE_3Ilsk,2076
scipy/io/tests/data/scalar_heap_pointer.sav,sha256=JXZbPmntXILsNOuLIKL8qdu8gDJekYrlN9DQxAWve0E,2204
scipy/io/tests/data/scalar_int16.sav,sha256=kDBLbPYGo2pzmZDhyl8rlDv0l6TMEWLIoLtmgJXDMkk,2072
scipy/io/tests/data/scalar_int32.sav,sha256=IzJwLvEoqWLO5JRaHp8qChfptlauU-ll3rb0TfDDM8Y,2072
scipy/io/tests/data/scalar_int64.sav,sha256=-aSHQRiaE3wjAxINwuLX33_8qmWl4GUkTH45elTkA-8,2076
scipy/io/tests/data/scalar_string.sav,sha256=AQ7iZ8dKk9QfnLdP9idKv1ojz0M_SwpL7XAUmbHodDQ,2124
scipy/io/tests/data/scalar_uint16.sav,sha256=928fmxLsQM83ue4eUS3IEnsLSEzmHBklDA59JAUvGK8,2072
scipy/io/tests/data/scalar_uint32.sav,sha256=X3RbPhS6_e-u-1S1gMyF7s9ys7oV6ZNwPrJqJ6zIJsk,2072
scipy/io/tests/data/scalar_uint64.sav,sha256=ffVyS2oKn9PDtWjJdOjSRT2KZzy6Mscgd4u540MPHC4,2076
scipy/io/tests/data/struct_arrays.sav,sha256=TzH-Gf0JgbP_OgeKYbV8ZbJXvWt1VetdUr6C_ziUlzg,2580
scipy/io/tests/data/struct_arrays_byte_idl80.sav,sha256=oOmhTnmKlE60-JMJRRMv_zfFs4zqioMN8QA0ldlgQZo,1388
scipy/io/tests/data/struct_arrays_replicated.sav,sha256=kXU8j9QI2Q8D22DVboH9fwwDQSLVvuWMJl3iIOhUAH8,2936
scipy/io/tests/data/struct_arrays_replicated_3d.sav,sha256=s3ZUwhT6TfiVfk4AGBSyxYR4FRzo4sZQkTxFCJbIQMI,4608
scipy/io/tests/data/struct_inherit.sav,sha256=4YajBZcIjqMQ4CI0lRUjXpYDY3rI5vzJJzOYpjWqOJk,2404
scipy/io/tests/data/struct_pointer_arrays.sav,sha256=fkldO6-RO2uAN_AI9hM6SEaBPrBf8TfiodFGJpViaqg,2408
scipy/io/tests/data/struct_pointer_arrays_replicated.sav,sha256=eKVerR0LoD9CuNlpwoBcn7BIdj3-8x56VNg--Qn7Hgc,2492
scipy/io/tests/data/struct_pointer_arrays_replicated_3d.sav,sha256=vsqhGpn3YkZEYjQuI-GoX8Jg5Dv8A2uRtP0kzQkq4lg,2872
scipy/io/tests/data/struct_pointers.sav,sha256=Zq6d5V9ZijpocxJpimrdFTQG827GADBkMB_-6AweDYI,2268
scipy/io/tests/data/struct_pointers_replicated.sav,sha256=aIXPBIXTfPmd4IaLpYD5W_HUoIOdL5Y3Hj7WOeRM2sA,2304
scipy/io/tests/data/struct_pointers_replicated_3d.sav,sha256=t1jhVXmhW6VotQMNZ0fv0sDO2pkN4EutGsx5No4VJQs,2456
scipy/io/tests/data/struct_scalars.sav,sha256=LYICjERzGJ_VvYgtwJ_Up2svQTv8wBzNcVD3nsd_OPg,2316
scipy/io/tests/data/struct_scalars_replicated.sav,sha256=lw3fC4kppi6BUWAd4n81h8_KgoUdiJl5UIt3CvJIuBs,2480
scipy/io/tests/data/struct_scalars_replicated_3d.sav,sha256=xVAup6f1dSV_IsSwBQC3KVs0eLEZ6-o5EaZT9yUoDZI,3240
scipy/io/tests/data/test-1234Hz-le-1ch-10S-20bit-extra.wav,sha256=h8CXsW5_ShKR197t_d-TUTlgDqOZ-7wK_EcVGucR-aY,74
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav,sha256=gjv__ng9xH_sm34hyxCbCgO4AP--PZAfDOArH5omkjM,3586
scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav,sha256=H0LLyv2lc2guzYGnx4DWXU6vB57JrRX-G9Dd4qGh0hM,3586
scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav,sha256=KKz9SXv_R3gX_AVeED2vyhYnj4BvD1uyDiKpCT3ulZ0,17720
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav,sha256=YX1g8qdCOAG16vX9G6q4SsfCj2ZVk199jzDQ8S0zWYI,72
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav,sha256=bFrsRqw0QXmsaDtjD6TFP8hZ5jEYMyaCmt-ka_C6GNk,1024
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav,sha256=zMnhvZvrP4kyOWKVKfbBneyv03xvzgqXYhHNxsAxDJ4,13
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-rf64.wav,sha256=GSJpCuezlvHbhP3Cr4jNWmz4zG46XZ6jci2fWtiMN0k,17756
scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav,sha256=9qTCvpgdz3raecVN1ViggHPnQjBf47xmXod9iCDsEik,17720
scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav,sha256=EqYBnEgTxTKvaTAtdA5HIl47CCFIje93y4hawR6Pyu0,7792
scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav,sha256=hGYchxQFjrtvZCBo0ULi-xdZ8krqXcKdTl3NSUfqe8k,90
scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav,sha256=BoUCDct3GiY_JJV_HoghF3mzAebT18j02c-MOn19KxU,70
scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav,sha256=R6EJshvQp5YVR4GB9u4Khn5HM1VMfJUj082i8tkBIJ8,1644
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav,sha256=t2Mgri3h6JLQDekrwIhDBOaG46OUzHynUz0pKbvOpNU,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-rf64.wav,sha256=iSGyqouX53NaEB33tzKXa11NRIY97GG40_pqWF_k5LQ,126
scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav,sha256=yCv0uh-ux_skJsxeOjzog0YBk3ZQO_kw5HJHMqtVyI0,90
scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav,sha256=oiMVsQV9-qGBz_ZwsfAkgA9BZXNjXbH4zxCGvvdT0RY,120
scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav,sha256=e97XoPrPGJDIh8nO6mii__ViY5yVlmt4OnPQoDN1djs,134
scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav,sha256=wbonKlzvzQ_bQYyBsj-GwnihZOhn0uxfKhL_nENCGNc,150
scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav,sha256=Uu5QPQcbtnFlnxOd4zFGxpiTC4wgdp6JOoYJ2VMZIU0,164
scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav,sha256=1F67h8tr2xz0C5K21T9y9gspcGA0qnSOzsl2vjArAMs,116
scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav,sha256=TJvGU7GpgXdCrdrjzMlDtpieDMnDK-lWMMqlWjT23BY,89
scipy/io/tests/data/various_compressed.sav,sha256=H-7pc-RCQx5y6_IbHk1hB6OfnhvuPyW6EJq4EwI9iMc,1015
scipy/io/tests/test_fortran.py,sha256=GcN2kEe42JNuTZEdXDGHv8gxgKnSb2z9Ny2Bj4MVcho,8873
scipy/io/tests/test_idl.py,sha256=e-MW2PmUwlp3JWkyA1hvpgNZfKg1Ag2rLrvAtaQ8d9M,21052
scipy/io/tests/test_mmio.py,sha256=fmflw3zcQaiqAH9UiwXqXIXfe_Eo5J0krQGZGULF7QQ,29864
scipy/io/tests/test_netcdf.py,sha256=wYYxRab-AMdarFYb84TIlhhoU9n0xwtIlfeWdnvfmrY,20009
scipy/io/tests/test_paths.py,sha256=aLSuVIBgbDHpgeuqvT_cAqAPQVb266T0oJHNLhvgJmk,3283
scipy/io/tests/test_wavfile.py,sha256=LzSXRMvNAstxz39e8KdSJoC8Ag8Yz3sEh8q-2Vl5Y60,17311
scipy/io/wavfile.py,sha256=TSQlI9WY_CVYlNvSs4QMgmJVGON6UdyIu6CgyTyGEVw,29538
scipy/linalg/__init__.pxd,sha256=SUm9fRHyF3s5-nhG0PWlenOlEEXtMRBEW_5X2q0ufGs,54
scipy/linalg/__init__.py,sha256=byYkkwVKNJuoJzNiksMlhQvd3RxaZU35EnJImC3bhlw,7753
scipy/linalg/__pycache__/__init__.cpython-311.pyc,,
scipy/linalg/__pycache__/_basic.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_cholesky.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_cossin.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_ldl.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_lu.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_polar.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_qr.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_qz.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_schur.cpython-311.pyc,,
scipy/linalg/__pycache__/_decomp_svd.cpython-311.pyc,,
scipy/linalg/__pycache__/_expm_frechet.cpython-311.pyc,,
scipy/linalg/__pycache__/_matfuncs.cpython-311.pyc,,
scipy/linalg/__pycache__/_matfuncs_inv_ssq.cpython-311.pyc,,
scipy/linalg/__pycache__/_matfuncs_sqrtm.cpython-311.pyc,,
scipy/linalg/__pycache__/_misc.cpython-311.pyc,,
scipy/linalg/__pycache__/_procrustes.cpython-311.pyc,,
scipy/linalg/__pycache__/_sketches.cpython-311.pyc,,
scipy/linalg/__pycache__/_solvers.cpython-311.pyc,,
scipy/linalg/__pycache__/_special_matrices.cpython-311.pyc,,
scipy/linalg/__pycache__/_testutils.cpython-311.pyc,,
scipy/linalg/__pycache__/basic.cpython-311.pyc,,
scipy/linalg/__pycache__/blas.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_cholesky.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_lu.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_qr.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_schur.cpython-311.pyc,,
scipy/linalg/__pycache__/decomp_svd.cpython-311.pyc,,
scipy/linalg/__pycache__/interpolative.cpython-311.pyc,,
scipy/linalg/__pycache__/lapack.cpython-311.pyc,,
scipy/linalg/__pycache__/matfuncs.cpython-311.pyc,,
scipy/linalg/__pycache__/misc.cpython-311.pyc,,
scipy/linalg/__pycache__/special_matrices.cpython-311.pyc,,
scipy/linalg/_basic.py,sha256=vFZS_gccJYZLvQrM3UDYW3CFhApQatbf5fjtPnyri1k,78204
scipy/linalg/_blas_subroutines.h,sha256=YvD5DtaiV0EWMiIiSk6Qq8y1wPYe-F_az_AC7HdgOZc,18354
scipy/linalg/_cythonized_array_utils.cp311-win_amd64.dll.a,sha256=RT9xZ9sxSz-QAHwQkurP5n_TX-SvYcBLcFtD3rRJYXY,1736
scipy/linalg/_cythonized_array_utils.cp311-win_amd64.pyd,sha256=9WM8wC1y2sukTr76zWxDEgYC-O3Ls55QshPI41LnxNM,577536
scipy/linalg/_cythonized_array_utils.pxd,sha256=fyp2eQgNSXX88dHrIV5qUuZ-fTEU-8JdXKOTLagAeK8,930
scipy/linalg/_cythonized_array_utils.pyi,sha256=scRj6ew53DtnyIry4gVV3IsZlbwiNZJeptv_0icKE2g,356
scipy/linalg/_decomp.py,sha256=7gjiZD0UpcUINQIrPbQpALS4HwCbVHbBz08jjFTeD-g,63513
scipy/linalg/_decomp_cholesky.py,sha256=y9dUF0yWlOfx8lJRImrhrfd92-Q8Y-5aj6ETQAhb0xE,14119
scipy/linalg/_decomp_cossin.py,sha256=wU1V8p4zwufI27vg3lc-Bxh1FbuQfW60Yzs_oj9n5ps,9198
scipy/linalg/_decomp_interpolative.cp311-win_amd64.dll.a,sha256=PyKIMLsPvucunwXhk0QOgxSCsQH4th9wQUaeY4Ns4YU,1712
scipy/linalg/_decomp_interpolative.cp311-win_amd64.pyd,sha256=-xRrn26Fo_K57R3cZMeURzkVXo8ooxixxMyNRRymyhs,985600
scipy/linalg/_decomp_ldl.py,sha256=fx4qmHmF_JuJ3R_r9aqcn7UZG4jTui6Vy5SqmIiD2Ns,12888
scipy/linalg/_decomp_lu.py,sha256=zj1apMhISBMDU1YZcrzNygk3k3uJ7-eZYim2mCl-kPA,13330
scipy/linalg/_decomp_lu_cython.cp311-win_amd64.dll.a,sha256=xZ9jKPpRSF4eCIYEplezmrfYvuu_Up28oJ9CqF3sRRg,1664
scipy/linalg/_decomp_lu_cython.cp311-win_amd64.pyd,sha256=aqDMU0-u2aWmm7HCa9jHNv9IpsncJt2Ye5YBs41w5ns,244224
scipy/linalg/_decomp_lu_cython.pyi,sha256=bwyBF7nl-jGGrmj_D16QSXx7MBXAVtU3k4eswqKohCA,297
scipy/linalg/_decomp_polar.py,sha256=4KAJQAgKoN6W_onEM-m3oGqfwCK3__ASC7UFMFdKcac,3689
scipy/linalg/_decomp_qr.py,sha256=IbbBbY_Wog5HBUS0akHRVP4BvNckkM3_dwDGpPzrRGs,15878
scipy/linalg/_decomp_qz.py,sha256=RMxX7pLyNuFhvTKwRnuaR0NraFwuZhvC8DBlIPc8I4Q,16779
scipy/linalg/_decomp_schur.py,sha256=zH3MxhMVsmY9mLKFfiSmKl932Qt87BuAxL0WhjxgVOA,12393
scipy/linalg/_decomp_svd.py,sha256=tl7O3Nducfoui2W0hnOt5XFbvCHljwnmZUZdIwL-zwY,17343
scipy/linalg/_decomp_update.cp311-win_amd64.dll.a,sha256=HamSEj1UX2t_40uf5AvtcOuCmQV9bMARZEkdhEskTY0,1628
scipy/linalg/_decomp_update.cp311-win_amd64.pyd,sha256=w5v-hWZrgBlb_cw82DoG33CO4pkqTb6k2S-RxRCiPA4,336384
scipy/linalg/_expm_frechet.py,sha256=UJWJkziLxetVgpLJucf4SrO5QXLzBXOZMkzis9lYyAY,12739
scipy/linalg/_fblas.cp311-win_amd64.dll.a,sha256=GrZhQIU2KT-ycJ-p8wYZgP5sivfZ7Re14GB19ue9nSs,1532
scipy/linalg/_fblas.cp311-win_amd64.pyd,sha256=3J7FRPpozOGTHZdiu55sy_WbYrm6TEd_ZsGt4LQTrP8,711168
scipy/linalg/_flapack.cp311-win_amd64.dll.a,sha256=D0lYL_FblkSHXwxi_NZc9QIn9aSPEPm9_2vwnTGRPQg,1560
scipy/linalg/_flapack.cp311-win_amd64.pyd,sha256=5hTJwgWDYsuQIani62hthVEMhscqWSLIdKUNvqnuwaE,2049536
scipy/linalg/_lapack_subroutines.h,sha256=m28-LKqeKyorgxl96RszhKAKBqbDSbvySmNMV_jnjfI,240854
scipy/linalg/_linalg_pythran.cp311-win_amd64.dll.a,sha256=cgPWG1QZynQXH5w-2QGhXqcFprkYmfZpLN7gmdJbOmc,1640
scipy/linalg/_linalg_pythran.cp311-win_amd64.pyd,sha256=dcOHoTsXYodcMsXDDVv73Rbwj3xIWeXzxoTTXJZ7VZ0,1061376
scipy/linalg/_matfuncs.py,sha256=Fq-YR9sddfclP_SP_67SSVvdVU0yc0XwEsEDnfZPDpA,26044
scipy/linalg/_matfuncs_expm.cp311-win_amd64.dll.a,sha256=dKUITgBeuI-mwWn_QDVQx1n-aUHS8oWb_6Mj6mVu3HQ,1628
scipy/linalg/_matfuncs_expm.cp311-win_amd64.pyd,sha256=ZmsvYGcD8oD3Cq0PQHwhzgiKfEPuFlOukhq2IbEQ_e4,229376
scipy/linalg/_matfuncs_expm.pyi,sha256=oR8iir6U3yDw0TBht7N_hOz2WtJpliJpf-goe8tRRqU,184
scipy/linalg/_matfuncs_inv_ssq.py,sha256=r9jQZ3UTugs8quso43y-elJ3jScg60XZu2oJuJ1CDsk,28981
scipy/linalg/_matfuncs_sqrtm.py,sha256=b0fgZwF6hRtcOjISIhYcFcNhoYuNW9w7VVjoICtu9DY,6473
scipy/linalg/_matfuncs_sqrtm_triu.cp311-win_amd64.dll.a,sha256=XhVcW2ZEFTCYBHgtfQacc3kgY9DGs_9K5t3qSx_EsXc,1704
scipy/linalg/_matfuncs_sqrtm_triu.cp311-win_amd64.pyd,sha256=jpr2bS22M_cVZ9EGamkCH9PMEMQtiezn2UHWVhtqums,256000
scipy/linalg/_misc.py,sha256=TQ8dy9SwwcUNl39NiworNgvWE6UoqW2mG2sOYW9cefE,6492
scipy/linalg/_procrustes.py,sha256=u2BYYe7efTpXgSlt36hZu1u6TojyXxhu1ij7qm505V8,3631
scipy/linalg/_sketches.py,sha256=Xv20BrafTmK1TIg0K6KHaOUM5o9YOxp5-8DMPsWE1C0,6295
scipy/linalg/_solve_toeplitz.cp311-win_amd64.dll.a,sha256=Raj609Yseh2vn4Le08YKlhs7RLRAT4N9dGzxxV5lYMg,1640
scipy/linalg/_solve_toeplitz.cp311-win_amd64.pyd,sha256=FL5rvIAUHVf7y2wMxPVgQa3BDTXnZDKErrYtTTcczqA,274432
scipy/linalg/_solvers.py,sha256=IPGlCDn1zanRD3JwenPNk4U8fb9yvbs5R8zSk6CgUp8,29657
scipy/linalg/_special_matrices.py,sha256=l5Or0BWBg588fmfazC4E36pP53RTWENTnWsVu4sPo-E,42029
scipy/linalg/_testutils.py,sha256=s8zF_A9X4ToKUuwd5QUToC1YoQg7QcFr5UgruaCat2U,1872
scipy/linalg/basic.py,sha256=cY1Q7GWjObxvdOHgB8EQWT3TiQbmXzWfyLOAOpvAGN4,776
scipy/linalg/blas.py,sha256=nX5TZ8folh34phVNhfQWi9W01u8bCZQIde6UinRDHVk,12169
scipy/linalg/cython_blas.cp311-win_amd64.dll.a,sha256=gwDDZW2W4wjAgX_Rypta2l-l7GiBijlJGawytLQhBoQ,1592
scipy/linalg/cython_blas.cp311-win_amd64.pyd,sha256=jZRBMoQpSB3cLYXonOtqhb9aJeKoxO7zvpfwBiUrgK4,279552
scipy/linalg/cython_blas.pxd,sha256=voPCvko12ZS_Bve1IvptSdsq6fu5KXa8ztkLU-zmVF4,15761
scipy/linalg/cython_blas.pyx,sha256=8dESd8wcDFsTuYBTDdOsYKjgPHECuLgeTntQJzvxWXA,66736
scipy/linalg/cython_lapack.cp311-win_amd64.dll.a,sha256=jHBZIU0tl89Ikrhn90v-4Si9YdMZV0_Yxp_lGQeybVk,1616
scipy/linalg/cython_lapack.cp311-win_amd64.pyd,sha256=_dkHRJbVCYOt9IpYiG9oIQROJ8hx2nktVoZLUPQ23sY,526848
scipy/linalg/cython_lapack.pxd,sha256=Up9I8aC8Q6olO0WmMfipi7g6micBTiKAztfpZ0zXOeE,206084
scipy/linalg/cython_lapack.pyx,sha256=XfsRB1Aw-8zVeZ_1m20rQNs8msDiHCtki2iurpKoZao,719057
scipy/linalg/decomp.py,sha256=lHjiR7giOLALFVRUnaLgUZtzkJlsOvdbgdxJSezWzJI,731
scipy/linalg/decomp_cholesky.py,sha256=aWQWVpSmXuxOlmibf4weokAbcumzt3B4bWHNcaSeKTU,670
scipy/linalg/decomp_lu.py,sha256=OpAhkh9LuGzeF3xsNrV7LnLNH1r9BKIRjEykPq6J7Mw,614
scipy/linalg/decomp_qr.py,sha256=dj9Ip_NL9_DhbTMvOu3Uf73KFxzAWw8Zk5AkW_x8Ig4,587
scipy/linalg/decomp_schur.py,sha256=BTSI6wpUMZewPGsaY_t_iJVWH00YO1unn3J8M5cEA0E,623
scipy/linalg/decomp_svd.py,sha256=6vTAujJiNDCu1-UTkPcPmWkT9s2BXSi9H9d_o-RZCow,652
scipy/linalg/interpolative.py,sha256=3kYXK81YeUu5HrAiX2i6GyDP--ubc201NMHbOp52fYQ,33746
scipy/linalg/lapack.py,sha256=Z_SfBuLsZQWTc-DWtk7DNB90p42TQHnISLYtVqEAars,16866
scipy/linalg/matfuncs.py,sha256=vS2L0NIDTE4Q8FSeE3uljLVUqmhsTXY_JUbTLvbYsuA,767
scipy/linalg/misc.py,sha256=SY3ronlv05KREUi0vhxT3ruSPUg6MZmRoZWgXJh-7ZQ,613
scipy/linalg/special_matrices.py,sha256=0gdutxoyqAuYHgKppzgGBgDS2pB1e7w5DwvkO7TDWD4,779
scipy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/linalg/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_blas.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_cython_blas.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_cython_lapack.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_cythonized_array_utils.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cholesky.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_cossin.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_ldl.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_lu.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_polar.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_decomp_update.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_extending.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_fblas.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_interpolative.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_lapack.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_matfuncs.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_matmul_toeplitz.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_procrustes.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_sketches.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_solve_toeplitz.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_solvers.cpython-311.pyc,,
scipy/linalg/tests/__pycache__/test_special_matrices.cpython-311.pyc,,
scipy/linalg/tests/_cython_examples/extending.pyx,sha256=TBlMNQgNLtDDyTwvxwLlTrqQ6v6IMkO47MuvZP2_6ao,910
scipy/linalg/tests/_cython_examples/meson.build,sha256=4qNEQVI1Ss_uIAscdgPlmr_uRpTmcgiH0s6XwWqp8dQ,697
scipy/linalg/tests/data/carex_15_data.npz,sha256=E_PhSRqHa79Z1-oQrSnB-bWZaiq5khbzHVv81lkBLB4,34462
scipy/linalg/tests/data/carex_18_data.npz,sha256=Wfg5Rn8nUrffb7bUCUOW7dMqWSm3ZPf_oeZmZDHmysY,161487
scipy/linalg/tests/data/carex_19_data.npz,sha256=OOj8ewQd8LI9flyhXq0aBl5kZ2Ee-ahIzH25P4Ct_Yc,34050
scipy/linalg/tests/data/carex_20_data.npz,sha256=FOIi00pxGMcoShZ1xv7O7ne4TflRpca6Kl7p_zBU-h0,31231
scipy/linalg/tests/data/carex_6_data.npz,sha256=GyoHNrVB6_XEubTADW2rKB5zyfuZE8biWBp4Gze2Avk,15878
scipy/linalg/tests/data/gendare_20170120_data.npz,sha256=o9-rRR2dXCAkPg7YXNi2yWV2afuaD4O1vhZVhXg9VbU,2164
scipy/linalg/tests/test_basic.py,sha256=atO_7Sm-O4NF4fvzgItHpvsA1scXe2ZKlkYfXKJ-UF8,80942
scipy/linalg/tests/test_blas.py,sha256=i4jUu6rC98WOiKXzQtRCUl6-QdcQlFRJvAhnBPbYipU,42856
scipy/linalg/tests/test_cython_blas.py,sha256=w-Xg6YtJKzlhfD5EKninnVnvmwJuDYnffM-kzotXoCE,4205
scipy/linalg/tests/test_cython_lapack.py,sha256=FuwDgBZSL9JtQSLjW5-0Bueom1sBYLd4iCwZT2EGX3k,818
scipy/linalg/tests/test_cythonized_array_utils.py,sha256=DhXfE4AUbdX9X5bDhcTvf43-haLv4ZrJ6sqBMshkOvc,4304
scipy/linalg/tests/test_decomp.py,sha256=FZvmjyIJem5pCAupubKX39nKNkrYLP_Ry3CihkUhjpA,121838
scipy/linalg/tests/test_decomp_cholesky.py,sha256=mqJiEJSLV72YnoI9Ht0pT6ZtJDgaRMW1VPlcTHUPDt0,10011
scipy/linalg/tests/test_decomp_cossin.py,sha256=PI92UwvHxlq4VS1EAoYQEN_9O8wQXY6ZW3aEWsQz3-0,12282
scipy/linalg/tests/test_decomp_ldl.py,sha256=IuARXEsPqa8nupnOYaM0PV4nXf63Yrx4GimZ_2aaXuY,5108
scipy/linalg/tests/test_decomp_lu.py,sha256=knHYT0tn4gcbke6ybHCEFh2qf0DgpyfaSrRS_g_45Uo,12937
scipy/linalg/tests/test_decomp_polar.py,sha256=Qbz419-_RBk609srtgYEMBI5i0XkvM264NeMciMgcQE,3397
scipy/linalg/tests/test_decomp_update.py,sha256=HYWHmA5zXN3kT_ULj9noBvkuyWL4HH1ythYS3aTjC4o,70203
scipy/linalg/tests/test_extending.py,sha256=Kqi89ZDGfyNf7De04K53Bmw2hwgUFJbMH6b1OCmzjis,1797
scipy/linalg/tests/test_fblas.py,sha256=FoEZpLFpdle1scpFXl5CeVTEH31c5GBxxW5FNPx_Usg,19294
scipy/linalg/tests/test_interpolative.py,sha256=ean0z223e7PvHQlRHHrAPDTcHZJ2jmzsso4OBbIosKQ,8809
scipy/linalg/tests/test_lapack.py,sha256=7REplOM7dBNlcVDU5pKL5qP-VhEPa5wideWLTCIn1-k,138289
scipy/linalg/tests/test_matfuncs.py,sha256=WWHXDv29DLl2hi5bleSlzq2TP8AeLews7IKakyZKSBM,43060
scipy/linalg/tests/test_matmul_toeplitz.py,sha256=U5eyz0CgfamqdrZsXCvY_qy736xhbwKgM0Oe3eybkdM,4224
scipy/linalg/tests/test_procrustes.py,sha256=N0Fbc1yMnahVavR9NJy0-dPQXe92oX1O7xvxvJpS-H8,7881
scipy/linalg/tests/test_sketches.py,sha256=hfaeXh4LMwA_xFfnvXqb4cUONe_z9lDmAVLKZ3FUyuA,4072
scipy/linalg/tests/test_solve_toeplitz.py,sha256=cPotfXd67SxR3e4TezV9DWnd8LSTDWiL9teiEsAUTpg,5272
scipy/linalg/tests/test_solvers.py,sha256=mkCUIauCdRjSWyHaO_SN1h5PrdznzOLRcKDXc3mp1RY,34795
scipy/linalg/tests/test_special_matrices.py,sha256=5ZY3_YpHSpZdB4PnOQEqCrKkcQEHumLEkNo5_ofz_sk,25714
scipy/misc/__init__.py,sha256=QxQqnYCRdiWp6_jsoOKHP4ypuHztbh-3z0r6fdxN4gw,141
scipy/misc/__pycache__/__init__.cpython-311.pyc,,
scipy/misc/__pycache__/common.cpython-311.pyc,,
scipy/misc/__pycache__/doccer.cpython-311.pyc,,
scipy/misc/common.py,sha256=3-Cc2N9gE_yUo98aeXqNgE_JBwWLLWORJumYiBfPHuQ,148
scipy/misc/doccer.py,sha256=mfBxIHwOGDIrxRJN3nh2d9bSo3Gud1Wd0yk-t-CQZVo,148
scipy/ndimage/__init__.py,sha256=LU05Bf-k2QCip235MI8k3iT-wYfA509IXxoPEuisbNE,5327
scipy/ndimage/__pycache__/__init__.cpython-311.pyc,,
scipy/ndimage/__pycache__/_delegators.cpython-311.pyc,,
scipy/ndimage/__pycache__/_filters.cpython-311.pyc,,
scipy/ndimage/__pycache__/_fourier.cpython-311.pyc,,
scipy/ndimage/__pycache__/_interpolation.cpython-311.pyc,,
scipy/ndimage/__pycache__/_measurements.cpython-311.pyc,,
scipy/ndimage/__pycache__/_morphology.cpython-311.pyc,,
scipy/ndimage/__pycache__/_ndimage_api.cpython-311.pyc,,
scipy/ndimage/__pycache__/_ni_docstrings.cpython-311.pyc,,
scipy/ndimage/__pycache__/_ni_support.cpython-311.pyc,,
scipy/ndimage/__pycache__/_support_alternative_backends.cpython-311.pyc,,
scipy/ndimage/__pycache__/filters.cpython-311.pyc,,
scipy/ndimage/__pycache__/fourier.cpython-311.pyc,,
scipy/ndimage/__pycache__/interpolation.cpython-311.pyc,,
scipy/ndimage/__pycache__/measurements.cpython-311.pyc,,
scipy/ndimage/__pycache__/morphology.cpython-311.pyc,,
scipy/ndimage/_ctest.cp311-win_amd64.dll.a,sha256=tIOOmdaHW-tpdjSBJK1gweBRQXn5Z6LkYzJpjL71Za4,1532
scipy/ndimage/_ctest.cp311-win_amd64.pyd,sha256=azqHdgFkcdJ1E8q9-s06eRgsedcvSxLWWRSpwx_tpyU,16896
scipy/ndimage/_cytest.cp311-win_amd64.dll.a,sha256=QeEF_1PBtSgxIjDSfMDm1s_-WLPKRmKFqWFQpWpOuXo,1544
scipy/ndimage/_cytest.cp311-win_amd64.pyd,sha256=DRoVlLPZKpj6KxMgJhUDMEPXH6VDiaeQz8RCBbl3hQI,75264
scipy/ndimage/_delegators.py,sha256=HDvhaV9EtDvoHbnE2kpB9JtfUumOxRfJFIJDFeRd1Xk,9553
scipy/ndimage/_filters.py,sha256=kZ3L_TzM-cK2EhAhTSfIKKSVKdt1pkMvLZyZRClG7GU,72947
scipy/ndimage/_fourier.py,sha256=PTcoyytgZc_dFBsPuYriCy6oJv5HVz85zdSppt4GogM,11572
scipy/ndimage/_interpolation.py,sha256=Iaa8GNWSGDdjRTaifTCXPJRIbBujv0e-SYLpeaTl7Wo,37673
scipy/ndimage/_measurements.py,sha256=HHo4eHdrppxoEdEteh_RIojjZaDOx01ihf_r7K6TMLo,57800
scipy/ndimage/_morphology.py,sha256=D27jiAgBBYlbnTG5a4aRJRXDfpVPixU6D9Hu_vRi0q8,103391
scipy/ndimage/_nd_image.cp311-win_amd64.dll.a,sha256=KP4ju2roYGQDwJ9vBwc47l7ayyM7vCNjMDinkFdAI-E,1568
scipy/ndimage/_nd_image.cp311-win_amd64.pyd,sha256=BggMzNY0mvi1mFE4e358XLBlhKTowGYmAdVdjwbMPOU,176128
scipy/ndimage/_ndimage_api.py,sha256=Gvk57ROOlyakFYlsk5LO13xilG9G0ebLDs5NyXjc7dA,602
scipy/ndimage/_ni_docstrings.py,sha256=wGIifoMxeUSM2T932eyFSgVV2nNfoFaN50c4gkWlgJE,8752
scipy/ndimage/_ni_label.cp311-win_amd64.dll.a,sha256=gXRayQ6rwgTgCa1RTeMSzmCVnBjAVlheVW9eaJ937QE,1568
scipy/ndimage/_ni_label.cp311-win_amd64.pyd,sha256=WWGm8cgeiKnMchZ6iei043wtxVxbgNz1UG5UALkniOA,411136
scipy/ndimage/_ni_support.py,sha256=038EOroZAzBsim-Q1NEaMBzK9-Pg8JA7sJaoEc6pE9U,5451
scipy/ndimage/_rank_filter_1d.cp311-win_amd64.dll.a,sha256=TajPJCvJq1OHEJdIHWOfTsym_C2TLnTtFltaGu5O2tc,1640
scipy/ndimage/_rank_filter_1d.cp311-win_amd64.pyd,sha256=pAX4xpunx0clQwbgtGzKyP5GPMX6q6xpNLxxZfcjD9M,145408
scipy/ndimage/_support_alternative_backends.py,sha256=WjI6li-FupsmQbZoebAh3epdLaGIjBI5PWSOWCUsOMs,2675
scipy/ndimage/filters.py,sha256=pBSTBZeUY3XAJCIFdL1UZmB3OM6KJld2jhzPVWHpOYs,1003
scipy/ndimage/fourier.py,sha256=Mg8ym6fd2BXBFSrUiwrW3GUoeDTYXbdOqQe75EJiKYw,620
scipy/ndimage/interpolation.py,sha256=t5_hbCDE3G06Fd-A7wozt_Tndjlbj3z6jRQPtjiBReo,686
scipy/ndimage/measurements.py,sha256=NNTrZLZSbU6hO42Tj49me-S1A-V-pnODbpxkm4ehLOI,812
scipy/ndimage/morphology.py,sha256=6axnW-p_Fp3Bm-21zYoaaz-1SdNxEPYCl8SoeRXwDQQ,992
scipy/ndimage/tests/__init__.py,sha256=nn22sAz766C8FTykt-e2NdCZ6gZ0417aBraFuJCW81M,326
scipy/ndimage/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_c_api.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_datatypes.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_filters.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_fourier.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_interpolation.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_measurements.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_morphology.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_ni_support.cpython-311.pyc,,
scipy/ndimage/tests/__pycache__/test_splines.cpython-311.pyc,,
scipy/ndimage/tests/data/label_inputs.txt,sha256=oBKOjlyOqkDERFefuyjlsPeVfGRyfmDc-uiawuMey4A,315
scipy/ndimage/tests/data/label_results.txt,sha256=hNPE8YOPm5MZ169I4c5JnG2cWxleOlKddG1VdA-Fn3Y,4603
scipy/ndimage/tests/data/label_strels.txt,sha256=fLjLCCb72QdX55kKssZdwg262SaV5NgdIG_Bn_gPHSg,294
scipy/ndimage/tests/dots.png,sha256=sgtW-tx0ccBpTT6BSNniioPXlnusFr-IUglK_qOVBBQ,2114
scipy/ndimage/tests/test_c_api.py,sha256=h5Ah-K1we_d3fyQyS7KFMy6Orsr56BxPnSx5qEBYGaQ,3840
scipy/ndimage/tests/test_datatypes.py,sha256=rvm8plHWjihVqxU1q1xG43szbC4ck3dq_VizBMCblG0,2886
scipy/ndimage/tests/test_filters.py,sha256=9L2HdOvZVQB0wpIkDE8QqZuOhGVrQnb1_503YykRwzA,128411
scipy/ndimage/tests/test_fourier.py,sha256=byhZxwdneWXddQSRIz_JxWuWq65DISA-ZgpBYTh461Q,7955
scipy/ndimage/tests/test_interpolation.py,sha256=Mm8RQqwEnjjReNqBJY7qeWiMFJc-JNBsoBnD7ZUfQvo,62165
scipy/ndimage/tests/test_measurements.py,sha256=nIYIarRMxXpT7UcgK2npxTSlb57xMAnfTmieKnwRLmY,60483
scipy/ndimage/tests/test_morphology.py,sha256=58CjnHg95Qvap2ZuQFRSqQlPL9QIIz-GvUlUh9xWU48,131658
scipy/ndimage/tests/test_ni_support.py,sha256=CFzk3WZinGB1dFpamDlzVkrJyBR7d7B3UUyZrhYyGI0,2589
scipy/ndimage/tests/test_splines.py,sha256=jOd34pPFKGI5g-R2e1CKEkDYulrV3wNwHbp6e00ZYzc,2629
scipy/odr/__init__.py,sha256=gHT9GMAwFz2ewCC0B1kTenNLG6g8CHNm__5z6vtF_zc,4456
scipy/odr/__odrpack.cp311-win_amd64.dll.a,sha256=RxCEPNA5JfSsn37Sv4xnlFoMw7scfHI6EC9JOLUbjzI,1568
scipy/odr/__odrpack.cp311-win_amd64.pyd,sha256=dI1QdqZ3D6OS7VhVjbGEKHDyxq7POnvNG1sJ5s3QN9U,689664
scipy/odr/__pycache__/__init__.cpython-311.pyc,,
scipy/odr/__pycache__/_add_newdocs.cpython-311.pyc,,
scipy/odr/__pycache__/_models.cpython-311.pyc,,
scipy/odr/__pycache__/_odrpack.cpython-311.pyc,,
scipy/odr/__pycache__/models.cpython-311.pyc,,
scipy/odr/__pycache__/odrpack.cpython-311.pyc,,
scipy/odr/_add_newdocs.py,sha256=nquKKPO9q-4oOImnO766H3wnLIN8dBZJfqPh8BgKJ_8,1162
scipy/odr/_models.py,sha256=Mi8SuWoWXIFqGi3BfgMSc_eu1GQfGRtXSvkeI9fblWQ,8115
scipy/odr/_odrpack.py,sha256=JS4iJhlalQTyAJ1mfE9KBRBk1q5Wqzhlsu9Wqf8z7t8,43650
scipy/odr/models.py,sha256=_7pQbo0FThkV9yo2NvXC21_SMbNqUiBjeUsxnd9PerM,610
scipy/odr/odrpack.py,sha256=NqRd2QtNcw1-gq4KpkbkddvMF3G78DxKGSFlJ8Day4Q,653
scipy/odr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/odr/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/odr/tests/__pycache__/test_odr.cpython-311.pyc,,
scipy/odr/tests/test_odr.py,sha256=tG1MmTJGdPr0uANnUUePkHZH8SplKdCGyN5wXQThpPs,22686
scipy/optimize/__init__.pxd,sha256=Go47l_Tk8YvXQxnuJNN6UmDEApEKgBUrplitjO4tsUM,40
scipy/optimize/__init__.py,sha256=OGwMoSAIsdo0TfJihMt4BtW5LxLJi94EPkZLZfqpn2w,13739
scipy/optimize/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/__pycache__/_basinhopping.cpython-311.pyc,,
scipy/optimize/__pycache__/_bracket.cpython-311.pyc,,
scipy/optimize/__pycache__/_chandrupatla.cpython-311.pyc,,
scipy/optimize/__pycache__/_cobyla_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_cobyqa_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_constraints.cpython-311.pyc,,
scipy/optimize/__pycache__/_dcsrch.cpython-311.pyc,,
scipy/optimize/__pycache__/_differentiable_functions.cpython-311.pyc,,
scipy/optimize/__pycache__/_differentialevolution.cpython-311.pyc,,
scipy/optimize/__pycache__/_direct_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_dual_annealing.cpython-311.pyc,,
scipy/optimize/__pycache__/_elementwise.cpython-311.pyc,,
scipy/optimize/__pycache__/_hessian_update_strategy.cpython-311.pyc,,
scipy/optimize/__pycache__/_isotonic.cpython-311.pyc,,
scipy/optimize/__pycache__/_lbfgsb_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_linesearch.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_doc.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_highs.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_ip.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_rs.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_simplex.cpython-311.pyc,,
scipy/optimize/__pycache__/_linprog_util.cpython-311.pyc,,
scipy/optimize/__pycache__/_milp.cpython-311.pyc,,
scipy/optimize/__pycache__/_minimize.cpython-311.pyc,,
scipy/optimize/__pycache__/_minpack_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_nnls.cpython-311.pyc,,
scipy/optimize/__pycache__/_nonlin.cpython-311.pyc,,
scipy/optimize/__pycache__/_numdiff.cpython-311.pyc,,
scipy/optimize/__pycache__/_optimize.cpython-311.pyc,,
scipy/optimize/__pycache__/_qap.cpython-311.pyc,,
scipy/optimize/__pycache__/_remove_redundancy.cpython-311.pyc,,
scipy/optimize/__pycache__/_root.cpython-311.pyc,,
scipy/optimize/__pycache__/_root_scalar.cpython-311.pyc,,
scipy/optimize/__pycache__/_shgo.cpython-311.pyc,,
scipy/optimize/__pycache__/_slsqp_py.cpython-311.pyc,,
scipy/optimize/__pycache__/_spectral.cpython-311.pyc,,
scipy/optimize/__pycache__/_tnc.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_dogleg.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_exact.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_krylov.cpython-311.pyc,,
scipy/optimize/__pycache__/_trustregion_ncg.cpython-311.pyc,,
scipy/optimize/__pycache__/_tstutils.cpython-311.pyc,,
scipy/optimize/__pycache__/_zeros_py.cpython-311.pyc,,
scipy/optimize/__pycache__/cobyla.cpython-311.pyc,,
scipy/optimize/__pycache__/elementwise.cpython-311.pyc,,
scipy/optimize/__pycache__/lbfgsb.cpython-311.pyc,,
scipy/optimize/__pycache__/linesearch.cpython-311.pyc,,
scipy/optimize/__pycache__/minpack.cpython-311.pyc,,
scipy/optimize/__pycache__/minpack2.cpython-311.pyc,,
scipy/optimize/__pycache__/moduleTNC.cpython-311.pyc,,
scipy/optimize/__pycache__/nonlin.cpython-311.pyc,,
scipy/optimize/__pycache__/optimize.cpython-311.pyc,,
scipy/optimize/__pycache__/slsqp.cpython-311.pyc,,
scipy/optimize/__pycache__/tnc.cpython-311.pyc,,
scipy/optimize/__pycache__/zeros.cpython-311.pyc,,
scipy/optimize/_basinhopping.py,sha256=6gzg7mFL74v6fYfs4v4YBML66LtgowtuaPrQg9VgMzA,30513
scipy/optimize/_bglu_dense.cp311-win_amd64.dll.a,sha256=gRMhKmyttPBmu4diAgPtnS4EOK1s0OKK23vVY1MBXJk,1592
scipy/optimize/_bglu_dense.cp311-win_amd64.pyd,sha256=vhphK_IM8XvK_3eGhqKX1s4s_30vtYcqdbxxfsBI-mw,317952
scipy/optimize/_bracket.py,sha256=n96RavC_GzLsVpCe_iMGNNTSzK6b8HkaqXjjhQXE-K8,32021
scipy/optimize/_chandrupatla.py,sha256=fOPox11BvtdoPpyyMBO6HABnXbZOdtULgm2TyIyd17g,25191
scipy/optimize/_cobyla.cp311-win_amd64.dll.a,sha256=GotNdqR10Tg-ZzbP1L6pnuAAvc5LCo5u0p3CPC5A08A,1544
scipy/optimize/_cobyla.cp311-win_amd64.pyd,sha256=gJizlofr0YBhOG1m8hRhiJ7eaoo1-slhgEUcaEeuoXI,418816
scipy/optimize/_cobyla_py.py,sha256=KJCc52YqUD5gdfK2HHYYGmslDVRsefmgOhydVK4rLVE,11183
scipy/optimize/_cobyqa_py.py,sha256=Ee6UNJ1U68TZODE4Wa2VsT48tQo1IiO_84B2JE5RWbY,3043
scipy/optimize/_constraints.py,sha256=oU2OMaZ5-6bdE61wFJZ9Uxs0Od4rZIpB6aQ295Mbykg,23489
scipy/optimize/_cython_nnls.cp311-win_amd64.dll.a,sha256=GBjvXH9q2eg8QUI9E75eyBsqNxiGNKGzd9vulmq0O2I,1608
scipy/optimize/_cython_nnls.cp311-win_amd64.pyd,sha256=I45yH4By5zyvLE-OoI2ijNbPfmD0HVkPvmkNyLOjvhw,101888
scipy/optimize/_dcsrch.py,sha256=HLVdwRymefDXa8QZQyZjca1y-yUWOfYn0Jh8SDs0RM4,25963
scipy/optimize/_differentiable_functions.py,sha256=jVjEfY-zUHpmYBNYC5S5e0jOp2qp_Uy--n6Y0ikzZx8,25616
scipy/optimize/_differentialevolution.py,sha256=7MWq38u5vFlxA9zTJiKGb_y-FPwbzOC5sAdWsdpFnZ8,88475
scipy/optimize/_direct.cp311-win_amd64.dll.a,sha256=_ug4HAYimkuhdi7pQxXooAkguz_te2oPxkxuGmLVX50,1544
scipy/optimize/_direct.cp311-win_amd64.pyd,sha256=FAq_3yge5Gp70Zvtxx6akxA8jVxPgtIcRGw9c3s8FCg,69632
scipy/optimize/_direct_py.py,sha256=ja2N3luv1NofWKa3tRzELii6SJqbBKUXrLMLdwxT5d4,12129
scipy/optimize/_dual_annealing.py,sha256=jtTU41-CaVpC8rsxq_DjQwRXAeE6vQJAMz1gzEaoQV4,31853
scipy/optimize/_elementwise.py,sha256=gBM__Qh9y0ZO3dnLExUxQfEXQpYDeyvY8MH_PH11zO0,33937
scipy/optimize/_group_columns.cp311-win_amd64.dll.a,sha256=IkvkV9JyJs294zAwb6oiONCwoZiV7AWx1OBmJbHkE6g,1628
scipy/optimize/_group_columns.cp311-win_amd64.pyd,sha256=phNnZBuMyLu5aNwnCBvbMis6-q7dZvVPTN5VLUCGz_U,1032192
scipy/optimize/_hessian_update_strategy.py,sha256=iRVRyZDBN5bNJxKAS-upjyqvGnsZCfq_oB-noOs57EE,18902
scipy/optimize/_highspy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_highspy/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_highspy/__pycache__/_highs_wrapper.cpython-311.pyc,,
scipy/optimize/_highspy/_core.cp311-win_amd64.dll.a,sha256=-eowwzNc0as-4JxAf11VCIiSJC5PTOp8Z7bWc1Yia9Y,1520
scipy/optimize/_highspy/_core.cp311-win_amd64.pyd,sha256=cmml7CLKTR8o0WFmxIEBRDetOw4fGFyC2dOcBmBpHXI,5780992
scipy/optimize/_highspy/_highs_options.cp311-win_amd64.dll.a,sha256=w17f3cQGgTMa_aKQ-n3io2kukr3PhMN3O1HGIc39zLc,1628
scipy/optimize/_highspy/_highs_options.cp311-win_amd64.pyd,sha256=-9jNhjqkTkWu8RcUSHs-SAjsWX-7p9ri1C_sSByN7xs,1221632
scipy/optimize/_highspy/_highs_wrapper.py,sha256=HypwHCVyXceLU_AYWsgC50901IRpXGwhJ9UdkHMCmU0,11632
scipy/optimize/_isotonic.py,sha256=4bN5DvfXn-ITUA8W0xDl4xoHSnNjiyAHX-J5uMrVvlY,6234
scipy/optimize/_lbfgsb.cp311-win_amd64.dll.a,sha256=yXh30_fJ4Ls9z4Cj0e96C_4LK5J60ETJHDBGWKwIrpE,1544
scipy/optimize/_lbfgsb.cp311-win_amd64.pyd,sha256=e3EAurKjncXHnaD2-waOSmJttQz85F4aQshWifQDl6k,171520
scipy/optimize/_lbfgsb_py.py,sha256=SJHbrIJZb-oWv-Xk-idnG9Klqvd7WJqS1iZCO3pPd4Q,21625
scipy/optimize/_linesearch.py,sha256=0Tou3QXyomA-ufSauAw7fkA-sL-F3bYlaDScJPMor-Y,28111
scipy/optimize/_linprog.py,sha256=7MoJum3RCa_1_6Ur60Qz3WPf4bfeoEfl--Or7nKK6Fs,30995
scipy/optimize/_linprog_doc.py,sha256=u5ji-u-dNKG9D58YlkOoWrSzft6zyKnEFt7lN-sao_o,63376
scipy/optimize/_linprog_highs.py,sha256=L0Yja8eMgOV5gNkG9kQS7S55gEx7L1fxEUHWVPKhR1g,17566
scipy/optimize/_linprog_ip.py,sha256=gZxACNoDQpWnj7Iw6gTq7CTK-WKZ2LeVFp_l53VMSic,46910
scipy/optimize/_linprog_rs.py,sha256=BHV1EnWBqNUMB2fsT6s6A5dke4w2GauDuBovFRvrB_c,23718
scipy/optimize/_linprog_simplex.py,sha256=8oWXIAYtNU0etv8PEJd9EjVwA4eHrCqyyMA0IL5TLV4,25411
scipy/optimize/_linprog_util.py,sha256=4X1XdIJ-klD77B1UlXAhNGUQ7jAqDokxQNUMOicxo4c,64322
scipy/optimize/_lsap.cp311-win_amd64.dll.a,sha256=LtFqXpx6n1H3qboC0jvEKaTfnWRI7ag9M0NX5KL_Iok,1520
scipy/optimize/_lsap.cp311-win_amd64.pyd,sha256=tzXkkdLCWsnwFMYXdYo3U2FqrFA_gQ8eNSm_qVO15aY,177664
scipy/optimize/_lsq/__init__.py,sha256=c4V-tnMyi2dXNNXkjFQ8SdvGANtgbrqXEM-OpdBdZpQ,177
scipy/optimize/_lsq/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/bvls.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/common.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/dogbox.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/least_squares.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/lsq_linear.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/trf.cpython-311.pyc,,
scipy/optimize/_lsq/__pycache__/trf_linear.cpython-311.pyc,,
scipy/optimize/_lsq/bvls.py,sha256=h9bYh3astIRvMtMGghtEGpG5fDsh74SMRbmgF3HN8YM,5378
scipy/optimize/_lsq/common.py,sha256=i8PFEtEhwGd7PArQZ6XOkxHGJnQB9kpf4SD-_iAGBkc,21207
scipy/optimize/_lsq/dogbox.py,sha256=KuyTxgJPRumBkrk8KlgZZVhm8tpdmsGNBtPAsj2U1Xs,12013
scipy/optimize/_lsq/givens_elimination.cp311-win_amd64.dll.a,sha256=g4wrm_BQxRZahJM71sCBVducyIpjcJbXhYpNBVCY3m8,1676
scipy/optimize/_lsq/givens_elimination.cp311-win_amd64.pyd,sha256=il8ufkIMqYwLn6BOIcDmpPuGuyO56kTrdbKWEJH4xTg,210432
scipy/optimize/_lsq/least_squares.py,sha256=Uv_4lsoX6iBMYm6Axeqkc1VELRe8oV-C74MNsyDI0Tg,40274
scipy/optimize/_lsq/lsq_linear.py,sha256=hmppGRSzBPh4ghT_NEQdMl7TKUwVNn-9LNRa1glE-hU,15398
scipy/optimize/_lsq/trf.py,sha256=BBsxUjWZjzdVILU-Zr98t3WiySH_MUIp-unCQlRAo6U,20037
scipy/optimize/_lsq/trf_linear.py,sha256=GEMGyMqWwaVLUa7sCo9iwDmUkuT746oci1iK6KjnJQA,7891
scipy/optimize/_milp.py,sha256=6YYxGucqJ4o3NJMubZ_p4AvMsf-JhX5Zkwj8XmW9tTs,15619
scipy/optimize/_minimize.py,sha256=fj2kz9pmMrN1sUdnLYwutepxfrnQIHPgaMWhBiUqv_E,51266
scipy/optimize/_minpack.cp311-win_amd64.dll.a,sha256=VI9cwwLofZujqb2Zgbh4FGKOlDdNSPT2gplOaMf4ie0,1560
scipy/optimize/_minpack.cp311-win_amd64.pyd,sha256=36WlyuXlfqtHWcuLjhvRFp0TdfTFY12p0dzI4AsFWGc,122880
scipy/optimize/_minpack_py.py,sha256=HzKL4a1ToxV0MkRITA1Kn66VTwVgaOxDGwrBRDcR_UI,46199
scipy/optimize/_moduleTNC.cp311-win_amd64.dll.a,sha256=DAqW4mm97vDJjIsz1B2tHNPUD1tGz26JqxAqXJGyNHU,1580
scipy/optimize/_moduleTNC.cp311-win_amd64.pyd,sha256=fzzn3OBCx-aTbIhNCCtR1wOo3-iuzOKeXh5o_ogmRYc,158720
scipy/optimize/_nnls.py,sha256=20GGtF7CYxP8hr79KAQ1GA9UhCE5rxsU79p-F-kkVA8,3330
scipy/optimize/_nonlin.py,sha256=6UkkJcEMd0smDmoIMSIm6gyjwF2ZWOfu1VUHygObIy4,51780
scipy/optimize/_numdiff.py,sha256=PTs2vtjQKlBScGfh73aYZCoHKQKQ-LHYO6Elm8iLFkM,29716
scipy/optimize/_optimize.py,sha256=sgHO3eWrT5gdUy4VKPdlZGUeSL0A6o5UmguHNN5e7E8,151816
scipy/optimize/_pava_pybind.cp311-win_amd64.dll.a,sha256=OEc0xcaBG-P2ADaAz_pJqssxlUL2MGJ7ZMz6fzEP55Q,1608
scipy/optimize/_pava_pybind.cp311-win_amd64.pyd,sha256=uUr-6WeJrRAjiodOOrqvBdpD59pcFuonkvCWxvQhEl0,288768
scipy/optimize/_qap.py,sha256=oRJM7pgx5KDNRuJ-AtBH3DufByhIFhqfs-2Z5icQWIM,30150
scipy/optimize/_remove_redundancy.py,sha256=wm3BTNUsnRTd-emKeiFTss68K09fIH4IYyFR7MD3cJg,19291
scipy/optimize/_root.py,sha256=fsXLPO77yz_0Vugh5e_SjefZd_xfktN4NMF1yxIMtKA,29446
scipy/optimize/_root_scalar.py,sha256=cgLUVLKvhA4Jtao4Adc9JxQ1OKmjkMw3grSU6lxA_rc,20929
scipy/optimize/_shgo.py,sha256=GqzEYmYKGamRpXnv_Nhrf-NFLqrhgHLOItTsdjdU2_c,63999
scipy/optimize/_shgo_lib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_shgo_lib/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_complex.cpython-311.pyc,,
scipy/optimize/_shgo_lib/__pycache__/_vertex.cpython-311.pyc,,
scipy/optimize/_shgo_lib/_complex.py,sha256=szXkdydF6lvNpuTmGXAGNbBaLdQ_nPgthgnfBBMGvuo,51449
scipy/optimize/_shgo_lib/_vertex.py,sha256=z9a-UXpMVD8NXa_y1ePp-Va3b7B1tcAQPmRcky1W2IA,14456
scipy/optimize/_slsqp.cp311-win_amd64.dll.a,sha256=skUUYxSSgtj9ig5XqurhnVRyMj58AWKHegTNEHMbk9A,1532
scipy/optimize/_slsqp.cp311-win_amd64.pyd,sha256=VCyTj7B2hpbKdyVI6yS8rSgFM1c6tBMe-fpI8Gto56I,108032
scipy/optimize/_slsqp_py.py,sha256=C_5ZKpoAuUc2zNXOFXGJWT94_u9bv0IdU_P_cwwyngU,19577
scipy/optimize/_spectral.py,sha256=j33TVc8_7styCVxKZP76_2C5CsdyFi5lD5TpeBbo54U,8392
scipy/optimize/_tnc.py,sha256=di7_gLvcXWCROLJE87OYg6n_CmtoWP1NJtkCJ9eEvhI,17440
scipy/optimize/_trlib/__init__.py,sha256=5TbV8UJSSEwW_qONgzozoHDgvhhugzqykJzybDCrNj0,536
scipy/optimize/_trlib/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_trlib/_trlib.cp311-win_amd64.dll.a,sha256=_y-JVEt7Th1zNoizj_PkLzqD0QQxJUiWfhkfjQ9u-jQ,1532
scipy/optimize/_trlib/_trlib.cp311-win_amd64.pyd,sha256=JR511lRAJSmmLqVLh1hdXyNyqA8rmPk_9ay2jY0GhcA,331264
scipy/optimize/_trustregion.py,sha256=teQLopT2_eSupezBMmPdXbAgkjfDAl9ik-4M8jgl7ug,11105
scipy/optimize/_trustregion_constr/__init__.py,sha256=Y2OLn2HBQ5rxpAfI-UjWZc4j_sDqjOBGmgdS2zAC_24,186
scipy/optimize/_trustregion_constr/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/canonical_constraint.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/equality_constrained_sqp.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/minimize_trustregion_constr.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/projections.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/qp_subproblem.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/report.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/__pycache__/tr_interior_point.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/canonical_constraint.py,sha256=lrGNBUrb2fMQATx0uGUPQBdVM8oHgEJFO7CKpWhj-MI,12939
scipy/optimize/_trustregion_constr/equality_constrained_sqp.py,sha256=D4dGtXcztXOsmD60ccXDKtTDDLCqcv-rH2HJrdNsLWE,9385
scipy/optimize/_trustregion_constr/minimize_trustregion_constr.py,sha256=FcmICdsXkZSJVugfDKNF_s2779-OX_glzjd3ZVvjHF8,26721
scipy/optimize/_trustregion_constr/projections.py,sha256=-1w4LbwrFnQc8xMhoyiBazu-YNydDqvDcBUMNsu7yx8,13576
scipy/optimize/_trustregion_constr/qp_subproblem.py,sha256=kzhhoadtUgeFmSoBKerKMnSgMSSpzHc_m43hkgqjTlg,23229
scipy/optimize/_trustregion_constr/report.py,sha256=YoydHVRTxO5p8jZ9JZUiZX4nnLkwo-usIsksFEz0B6Y,1831
scipy/optimize/_trustregion_constr/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/_trustregion_constr/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_canonical_constraint.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_nested_minimize.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_projections.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_qp_subproblem.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/__pycache__/test_report.cpython-311.pyc,,
scipy/optimize/_trustregion_constr/tests/test_canonical_constraint.py,sha256=z8MKN6NbaxsH7pj2rbCGdOQ4jXE-eM-SPu_IcNMtIaI,10165
scipy/optimize/_trustregion_constr/tests/test_nested_minimize.py,sha256=fRerrlL48GskudreW9Yu7YI7PG9_bLuQ5ZT97CBks7o,1255
scipy/optimize/_trustregion_constr/tests/test_projections.py,sha256=YQ5iAUYXSTKZusnJxG8yD3Ctyb2qlCPm9IAk8y8pkAY,9048
scipy/optimize/_trustregion_constr/tests/test_qp_subproblem.py,sha256=N-fr5Ap01iijAdaDCaiAaKfN1rZtp1CpE2j9NLoRBEg,28364
scipy/optimize/_trustregion_constr/tests/test_report.py,sha256=6sn33lkn-_hJo5_5i7vGPZtlguF0ikfxAU_0_GLkMNo,1138
scipy/optimize/_trustregion_constr/tr_interior_point.py,sha256=aT2NXZZx6Yfrr_4cCWFUjRdJCsbBDcWND_qQCu-wxQY,14761
scipy/optimize/_trustregion_dogleg.py,sha256=XTvNQtebZTPzl6xGtzHBLDcFD2qe5sSOFOTbmtDWswo,4511
scipy/optimize/_trustregion_exact.py,sha256=gNpOe4iE1Bd_WxTlHt1wzhC2IZLSxubIrU52sl3eDe8,15996
scipy/optimize/_trustregion_krylov.py,sha256=OE6ABN1_oeRUUBXWGBA2QFlzxO4GJVBseoNOMB2b4Sw,3095
scipy/optimize/_trustregion_ncg.py,sha256=O2_8p92l_faUHLI26GHK2wV0RTdAR1scli0XhXTTsSo,4706
scipy/optimize/_tstutils.py,sha256=ikvxL4pBR4fy00TDcTdWafpTl8SVcPgdIeIC89LJ2ks,35019
scipy/optimize/_zeros.cp311-win_amd64.dll.a,sha256=-8I3nB-WK-kvQBL482w1cFjjsg7vi64u-gWPpnk5t88,1532
scipy/optimize/_zeros.cp311-win_amd64.pyd,sha256=jXfVTkhzVR1uSzd6kmX8tk9sDZu-8rwlvWxWO6NJaq4,23040
scipy/optimize/_zeros_py.py,sha256=Lx6PVHEklStsK5EvWK3VVhPj2CCr-J66A77q93gPBWc,53461
scipy/optimize/cobyla.py,sha256=CHOea_YiOpCNJ5blRI6YqoYieCohU96MNYV-YiWADGc,576
scipy/optimize/cython_optimize.pxd,sha256=FhEeI0aq_Y2P00BpghHKOa7UjhW_uv4e7aEzXdS-QjU,453
scipy/optimize/cython_optimize/__init__.py,sha256=LEyUcZ_tUfRNMYgyFBYUsHfztui15TviJlLCsbZWrDw,5020
scipy/optimize/cython_optimize/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/cython_optimize/_zeros.cp311-win_amd64.dll.a,sha256=N6xzOlQBwBW1oeV5EgC8KxpH0HriuouIrSQpTKI96Ok,1532
scipy/optimize/cython_optimize/_zeros.cp311-win_amd64.pyd,sha256=5CuXgDJTwp_wzZ7cVEhdXLpVLVS4Y7faJvkOTHIJgKc,92672
scipy/optimize/cython_optimize/_zeros.pxd,sha256=5o3CUJecdwYcTh6bX9c4O8F04MtmvvW9KluxtFsYDh4,1272
scipy/optimize/cython_optimize/c_zeros.pxd,sha256=kbSds0gdPDP4-oM6U2AaaFhsuEhCDXkzlfZ4BS82sHE,1144
scipy/optimize/elementwise.py,sha256=PQrGJ57URCLe60H3ogS-0aYT18l8Y8j_2BNM_2Qafbk,1228
scipy/optimize/lbfgsb.py,sha256=wRXQnUcc8-ePQOMmqVM1D0h-ghuhD_qyW_d35nr6YeA,624
scipy/optimize/linesearch.py,sha256=KiorxXY20ql-mVkDQUdUppTlg_KBES3pCfJHNL8XZwA,553
scipy/optimize/minpack.py,sha256=uxAnCG7Su9Gxi5u8P-BCbAe-qKwkNKtMvIkRoPdrRRk,691
scipy/optimize/minpack2.py,sha256=NTrxn0tvN_vBTHwPu5mXNV74MKj9MSEVt8xQHzpOuWo,531
scipy/optimize/moduleTNC.py,sha256=0BZtj41NEa1nM2XYN9Df-kmG7W1QJG4PWcY40Zour-s,526
scipy/optimize/nonlin.py,sha256=mtj4mAfZhUjTF_12P3dwy3ZWgyteugyqG8oJBKwkhn0,739
scipy/optimize/optimize.py,sha256=IJdwFy_tYftU9lXIecQaBpWWXNsbeWTYuZJ0P5KkYNI,917
scipy/optimize/slsqp.py,sha256=mtkavACQ1Y3Difti8ztWSwdZMA_GCamqGIxw7CXm31k,605
scipy/optimize/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/optimize/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__basinhopping.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__differential_evolution.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__dual_annealing.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__linprog_clean_inputs.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__numdiff.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__remove_redundancy.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__root.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__shgo.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test__spectral.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_bracket.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_chandrupatla.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_cobyla.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_cobyqa.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_constraint_conversion.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_constraints.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_cython_optimize.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_differentiable_functions.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_direct.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_extending.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_hessian_update_strategy.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_isotonic_regression.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_hessinv.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lbfgsb_setulb.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_least_squares.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_linear_assignment.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_linesearch.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_linprog.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_common.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_lsq_linear.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_milp.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_minimize_constrained.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_minpack.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_nnls.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_nonlin.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_optimize.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_quadratic_assignment.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_regression.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_slsqp.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_tnc.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_exact.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_trustregion_krylov.cpython-311.pyc,,
scipy/optimize/tests/__pycache__/test_zeros.cpython-311.pyc,,
scipy/optimize/tests/_cython_examples/extending.pyx,sha256=hYoYg2ZMzOBFvTBgsEbe8HEcgmRakP-eCTH5wOdZzE0,1357
scipy/optimize/tests/_cython_examples/meson.build,sha256=IJJmerGghNtTPz5ud2RIKKjNqxLnFlbZxHlOjD2XXAc,735
scipy/optimize/tests/test__basinhopping.py,sha256=N0XAFg5ljPSmZ-UFpsLel-prv1Z0yWVTbxZZRkiSjxM,19745
scipy/optimize/tests/test__differential_evolution.py,sha256=tfeZKcKaOzz3YIdvCy0KCaVhCozKESdiOuhAl3X6DdM,71225
scipy/optimize/tests/test__dual_annealing.py,sha256=6wYdaJcXGcfegzZHLNw_mjDwzPLIx7NEwj0hAJmnRzE,17056
scipy/optimize/tests/test__linprog_clean_inputs.py,sha256=8l7kHLqk5mqiRVkv8OLlMQpPXjNJOBWi-jkaWD5n2D0,11988
scipy/optimize/tests/test__numdiff.py,sha256=rUdNJZyBcuHLritcEyIsn41hNhi3X5dwu9hh37IyFK0,33200
scipy/optimize/tests/test__remove_redundancy.py,sha256=VC2Tz00TMHan0FJ8QXgKdvazARH58aWCLb97BQZyd08,7027
scipy/optimize/tests/test__root.py,sha256=FGsMKEz4Yju_c3U-3-kzx5P1IyQ8ik3bsH6gSiE9OII,4354
scipy/optimize/tests/test__shgo.py,sha256=FqImq1Bpt6HmKnd5ZQ5Zu482rcQqHBKa_vBosFXXH_Q,40960
scipy/optimize/tests/test__spectral.py,sha256=B3d_qUmY55_NiIrAljlKrk4kHDxP8iyb8PVAXFsx_U8,6890
scipy/optimize/tests/test_bracket.py,sha256=ZKhKhhmWSWwKu0qNtAg8G1Y1Ya7ndpns8a4mrGEBkvo,37949
scipy/optimize/tests/test_chandrupatla.py,sha256=Fe5ch6Rr6Am4UDDPTlZjL1ZQYRKxvZU2a4Wxi1xynvI,40004
scipy/optimize/tests/test_cobyla.py,sha256=W-bJIMeB41zU_kEhpjsMe5zQRhWSm5LZN0viyindsn8,5438
scipy/optimize/tests/test_cobyqa.py,sha256=B3BkBXExNEEAkIz-ermiq8i_DhIOHRNpoq1V1y7AB9o,8395
scipy/optimize/tests/test_constraint_conversion.py,sha256=ZkppwXo1bLUVhLUUPqsEz6_hvHwXIRHhYvKzc5KCEGY,12849
scipy/optimize/tests/test_constraints.py,sha256=cn-3BR5et1YJ4FSgvu8bVFsSmLeSqzoVDbcBlR6AvL8,9663
scipy/optimize/tests/test_cython_optimize.py,sha256=XmUrnJrxWKWfH5337FiCAfCV6djffjpQWVg1olG1vg0,2730
scipy/optimize/tests/test_differentiable_functions.py,sha256=lGWUVF4cJ0WdeNhOzzNKij3hvIJqoAci_2L6nsKwGeg,29294
scipy/optimize/tests/test_direct.py,sha256=W3GWt8VWR-7IP6tFOily5z84z7zZ_fyWYBAdHM3OF-E,13588
scipy/optimize/tests/test_extending.py,sha256=6bUmDqR2TCRMQhYPNcJL4QEkR7JsiJvkYGQazpb6R2A,1132
scipy/optimize/tests/test_hessian_update_strategy.py,sha256=FGxQB-IpuoqUkIa9LeP89aBPV4jiGMqLr8zCw9agNU4,14637
scipy/optimize/tests/test_isotonic_regression.py,sha256=7_lGuoWwYwOdLbUCuezoumQj8haAFMe7RJmK5ztsoOk,7280
scipy/optimize/tests/test_lbfgsb_hessinv.py,sha256=8_Bvj0kpB1mvK3fmh3v_vsYCrH8iFTnmaVjqe1EnSGA,1180
scipy/optimize/tests/test_lbfgsb_setulb.py,sha256=ztwwQ_5iPgLWBs02cSR9f4djZDBFZmlML_Ln6RzDhyo,3704
scipy/optimize/tests/test_least_squares.py,sha256=6mnmAVSWR0ZaT_i4bIrf5nD-TKy22LPME4oBnq2Ab8c,34895
scipy/optimize/tests/test_linear_assignment.py,sha256=51pamAv4Kf0zrqIUkxk-8iFVF4-iZS6F8CxVH1h0J1A,4201
scipy/optimize/tests/test_linesearch.py,sha256=Pt7ElmTI5-Nr5X4kPsBer8sDavUB5-lcW_FYTEx3O_A,11728
scipy/optimize/tests/test_linprog.py,sha256=TEEcF_aG6FKjqflfhAsZ3QFR4IpY2ot15nh172HYKhs,105255
scipy/optimize/tests/test_lsq_common.py,sha256=AXdfzIlCO5gQa3gy7Ou9mp1SAHD8-qMP1yCyfK1kaI4,9797
scipy/optimize/tests/test_lsq_linear.py,sha256=msAqZupAxi_98P_N5VrtWv5RhBsBdZmi_eDEOjSPhNk,11246
scipy/optimize/tests/test_milp.py,sha256=YeH08QZ0OZLU4ZhgJUJM5QBM2ItKnYXl4TohCjz5IKo,18761
scipy/optimize/tests/test_minimize_constrained.py,sha256=Imt2pZic1GLIJO7M6at4xRqDBy2f0vp955w8agsZsCM,28787
scipy/optimize/tests/test_minpack.py,sha256=lKEp6-_gTbkqqzKUqlf8McdKUrFztUpPUd7hDHIseU8,46035
scipy/optimize/tests/test_nnls.py,sha256=GoD6F97RWFsK_fFqMNm_XBW4Uz820vCEsHEnlfwphx0,26192
scipy/optimize/tests/test_nonlin.py,sha256=yiyCkeSrzDW15SUWt5xhMRUoiDGrzrmqDIEF2ZtZx94,19095
scipy/optimize/tests/test_optimize.py,sha256=tdjzqqfjp8rMEiPza_yAlYUO47MAcB-sccyPtmbMPVc,130728
scipy/optimize/tests/test_quadratic_assignment.py,sha256=WHdALCFsgRvZjb0Si9eQ_d3uZMIgbza0djZOrjiRcJI,18053
scipy/optimize/tests/test_regression.py,sha256=jc-uV89QavAhf34j8J2riNxYY9gIIgOddrRMV5ltC0Y,1117
scipy/optimize/tests/test_slsqp.py,sha256=zbajJZKadhSRq32SsjfopnqFH3AgcymYig9QGluQDBc,24131
scipy/optimize/tests/test_tnc.py,sha256=XWL1j_u_9ZrkUBOfyFVytIAdD_dw5rRo-0w5IHx_7PM,13045
scipy/optimize/tests/test_trustregion.py,sha256=8zX4bUi0eABzmaRzoOazGeH3fZV9FtZxjqxrQR6AdKQ,4779
scipy/optimize/tests/test_trustregion_exact.py,sha256=-2e4G5fjJDK69aLXNpgPw7LEXwpINSW7fZYN26exSSQ,13284
scipy/optimize/tests/test_trustregion_krylov.py,sha256=-zfEH2BIq6H9quqB81wtuPTNmdA9Pnl5Cc2jScMMGik,6786
scipy/optimize/tests/test_zeros.py,sha256=j-K7j1qKUpzfo6zeE90FMHGfPEW3CBxgeDgvb0vDzSM,37725
scipy/optimize/tnc.py,sha256=znhjH4IV17iV6ghdxwE0aj617iodH6OPPL_Afrj88LU,582
scipy/optimize/zeros.py,sha256=yZg_vt7rR_8z5N2i-Oam3rbMiPdas8l9xgib_sAAThk,646
scipy/signal/__init__.py,sha256=Hb7JIrteaROtUADxmFwKwgrvbWWPz8qOgN6afwlgtjU,13806
scipy/signal/__pycache__/__init__.cpython-311.pyc,,
scipy/signal/__pycache__/_arraytools.cpython-311.pyc,,
scipy/signal/__pycache__/_czt.cpython-311.pyc,,
scipy/signal/__pycache__/_filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/_fir_filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/_lti_conversion.cpython-311.pyc,,
scipy/signal/__pycache__/_ltisys.cpython-311.pyc,,
scipy/signal/__pycache__/_max_len_seq.cpython-311.pyc,,
scipy/signal/__pycache__/_peak_finding.cpython-311.pyc,,
scipy/signal/__pycache__/_savitzky_golay.cpython-311.pyc,,
scipy/signal/__pycache__/_short_time_fft.cpython-311.pyc,,
scipy/signal/__pycache__/_signaltools.cpython-311.pyc,,
scipy/signal/__pycache__/_spectral_py.cpython-311.pyc,,
scipy/signal/__pycache__/_spline_filters.cpython-311.pyc,,
scipy/signal/__pycache__/_upfirdn.cpython-311.pyc,,
scipy/signal/__pycache__/_waveforms.cpython-311.pyc,,
scipy/signal/__pycache__/_wavelets.cpython-311.pyc,,
scipy/signal/__pycache__/bsplines.cpython-311.pyc,,
scipy/signal/__pycache__/filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/fir_filter_design.cpython-311.pyc,,
scipy/signal/__pycache__/lti_conversion.cpython-311.pyc,,
scipy/signal/__pycache__/ltisys.cpython-311.pyc,,
scipy/signal/__pycache__/signaltools.cpython-311.pyc,,
scipy/signal/__pycache__/spectral.cpython-311.pyc,,
scipy/signal/__pycache__/spline.cpython-311.pyc,,
scipy/signal/__pycache__/waveforms.cpython-311.pyc,,
scipy/signal/__pycache__/wavelets.cpython-311.pyc,,
scipy/signal/_arraytools.py,sha256=W7k_0e3AW8Lz3RLPAmi4SXnJMge9N3y1-VZcvetm2jM,8558
scipy/signal/_czt.py,sha256=dhVmn1ScMOn8rzcvaou8n5s4ik8mlPH7BPsViaSA_so,20020
scipy/signal/_filter_design.py,sha256=mN0BoeWjf9tB_4UAmc8wbkkEm6ZiX_n-Y66mk4DWqmQ,193660
scipy/signal/_fir_filter_design.py,sha256=JR16ieAPXblN4ohvsbnIeHOdVMDFjP1tPzqDwKKzkFI,51027
scipy/signal/_lti_conversion.py,sha256=KMZcOvKw71pnIf5y_AlAXyOzFd-Rd2aMe0D7yUFKLzs,16590
scipy/signal/_ltisys.py,sha256=BvUqGNlOm9Fk18MZkVOtf_kRpklllT57XDBo0440meo,121570
scipy/signal/_max_len_seq.py,sha256=oVUkL3qSfM1_FDWY_AcUI5fFi2vBQH0f1iXiDVOrIbE,5199
scipy/signal/_max_len_seq_inner.cp311-win_amd64.dll.a,sha256=7uJQhlYta-RNVbUxGyhG1KEEAd7qro6lOGflWm0FoaA,1676
scipy/signal/_max_len_seq_inner.cp311-win_amd64.pyd,sha256=_bgw_AeOfPLdRTu8Nv5XoLuDByNnVjpVk_CY6xlBfPc,1006080
scipy/signal/_peak_finding.py,sha256=gjjLiuiho1HNgze2IUeFsooxDYXk0RXU2DZ8QPDjvgI,50166
scipy/signal/_peak_finding_utils.cp311-win_amd64.dll.a,sha256=hOo8KHnh-2uPuuNo7mxTR7N75O87WClI5g7f6tEkPmc,1688
scipy/signal/_peak_finding_utils.cp311-win_amd64.pyd,sha256=Lb8roY7pj1PEOGjqCzISXMMq453-Ny79Oip_f1dssHE,275968
scipy/signal/_savitzky_golay.py,sha256=iagtdjN0dhETVa7ekXTPkZqmgRK6ncfJViq__xDiL00,13804
scipy/signal/_short_time_fft.py,sha256=msYUyed345R7OiWISTdYPOaIbz9LCYf-BK2CbAFwEpg,78044
scipy/signal/_signaltools.py,sha256=vtnZBf78jet3m1jXT3g7R0M_yvg25CZrShLpS1m9roc,181439
scipy/signal/_sigtools.cp311-win_amd64.dll.a,sha256=BNaGSd65KIYjvCdXvXY4Wul2ook4J9SlE47D3nLvKbY,1568
scipy/signal/_sigtools.cp311-win_amd64.pyd,sha256=FnUVl_iCY_UApw0Oj4nsQtz2XRH_NkuRPe1qnfzMajc,117760
scipy/signal/_sosfilt.cp311-win_amd64.dll.a,sha256=NIh2zwrweQ3_A_UoV46SuoVNbhmzKJM9x8tVMoaqUYg,1560
scipy/signal/_sosfilt.cp311-win_amd64.pyd,sha256=RWTX2l1ATBhjhEnmEKKLRzDuqKhayGpX33e4dDF19H8,280576
scipy/signal/_spectral_py.py,sha256=krf9I6PCYrtq3e8r7wvLuC7Ww-E7AzmdwS75MU4StUI,89188
scipy/signal/_spline.cp311-win_amd64.dll.a,sha256=qKujgKY9CwzyPuJw8cdXIcjzaGyDTx2KTIDkOusOats,1544
scipy/signal/_spline.cp311-win_amd64.pyd,sha256=9J0PCGcHNuv-e8fjkq2bAYKnz-d069GD02vfbDWxeIk,63488
scipy/signal/_spline.pyi,sha256=GCkRUtUAxpa7e_6K0t6MtqFcwymDm-EMMfjXMHb3xjM,982
scipy/signal/_spline_filters.py,sha256=YF5en1K59qr69SatLmW7bGwkNfDDkRpQDhv1RiPDhf8,25295
scipy/signal/_upfirdn.py,sha256=SZeLNVfPAOjmX6Bqjh0xvoUehjYcPFpJ3wwLwqf7RJg,8098
scipy/signal/_upfirdn_apply.cp311-win_amd64.dll.a,sha256=bzRi-uBJuuKacDqn18P9mqMjqD46Vkdh05gHPGR8r7U,1628
scipy/signal/_upfirdn_apply.cp311-win_amd64.pyd,sha256=V4Vkm_bYQ2EH6gpFkXpleK3sljT_OcYJWpZtT9aIJLE,368128
scipy/signal/_waveforms.py,sha256=-0XZzMme7YCKMwHfhmKz4m_PrZHo7HaqkmKTq-eutR4,23785
scipy/signal/_wavelets.py,sha256=X-236R-1ggCFxu57t_HU2VIgFgyK3zQ2RtKxcTwVWsc,902
scipy/signal/bsplines.py,sha256=loef-iAMQV1REr5EwbCNDST0FHLHgHnwS246fkgZnSc,672
scipy/signal/filter_design.py,sha256=N6PHV8A26WatoX80yg-0iFfuDC1-sPiyHs4FHsrhQ1Y,1140
scipy/signal/fir_filter_design.py,sha256=3A64W0mSoJBgVu-v_KJEIVr7DHDtba4p2PVpClixLQI,660
scipy/signal/lti_conversion.py,sha256=6wCVjM9xgyn3y5YrXT6aPFOUHSISDkNFaSFsVme8BSk,659
scipy/signal/ltisys.py,sha256=hA9Q6qciqgKHRLeUSOedGx9q9jN0dWyERJ3F19_ia_s,894
scipy/signal/signaltools.py,sha256=Ia88ONqXpX4qZyYd364I2CZQyqn2Mqwa5rCOihEhDzc,1065
scipy/signal/spectral.py,sha256=Xf30DdZVzd-sp4GIPvCiyulUJIYNo-x7LxeqlyQLQIE,683
scipy/signal/spline.py,sha256=nwc2lUp0B6LTPUZeQEHrk1gSSBGsV5CnyglCLseEFbU,772
scipy/signal/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/signal/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/signal/tests/__pycache__/_scipy_spectral_test_shim.cpython-311.pyc,,
scipy/signal/tests/__pycache__/mpsig.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_array_tools.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_bsplines.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_cont2discrete.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_czt.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_dltisys.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_filter_design.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_fir_filter_design.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_ltisys.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_max_len_seq.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_peak_finding.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_result_type.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_savitzky_golay.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_short_time_fft.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_signaltools.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_spectral.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_splines.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_upfirdn.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_waveforms.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_wavelets.cpython-311.pyc,,
scipy/signal/tests/__pycache__/test_windows.cpython-311.pyc,,
scipy/signal/tests/_scipy_spectral_test_shim.py,sha256=pMn4fJkKtgH8x88OJWaaSSYsTnlwJ9q5HyZxn8ZJ5cs,20120
scipy/signal/tests/mpsig.py,sha256=3cd_WPgz9dkjalruhXxxAPYCV8Kzs0L8CH4h-0W8jX4,3430
scipy/signal/tests/test_array_tools.py,sha256=vdpsiDEwAiyI76Z1cHjTy4Qo8mVJvWY01oy6Et6q9Iw,3700
scipy/signal/tests/test_bsplines.py,sha256=d0rXw0bku9iscZZ41LRxzmApnm88dYQ4qqd4CZFo8Sc,16256
scipy/signal/tests/test_cont2discrete.py,sha256=0GxrQTOencV4bIigT86yF6LpGSW5apHDaaDGoMHZn0E,15089
scipy/signal/tests/test_czt.py,sha256=uihNKvHxZWJE__J36TMtE-9Zo-w2nMM-ZB9Ns7P8658,7377
scipy/signal/tests/test_dltisys.py,sha256=okDw2DtC6EVroj8WDcrhUECXnNhocuBKGbzi_UdWML8,22082
scipy/signal/tests/test_filter_design.py,sha256=TJxPCdS7DzqD-lK8bO7J5nQgx3pZkwQmRcqqVlZIC6Y,202694
scipy/signal/tests/test_fir_filter_design.py,sha256=j86t4E11Mmh3K1uvb72EXjl3dhldSTEpiqO4Z47dO-w,28386
scipy/signal/tests/test_ltisys.py,sha256=a2XgQz9UBkOHhCthMu35E3V81xr3JuPiNAJnwFMCEzM,46295
scipy/signal/tests/test_max_len_seq.py,sha256=0nu9ZP7BzyaUQKFIacGAtyYDTZYNV1X3mFRTTJmXrLs,3389
scipy/signal/tests/test_peak_finding.py,sha256=YG5-lneGD3P7pdg0xtgAIJTsCR1OyaVMeV6DiPgb_Xk,36991
scipy/signal/tests/test_result_type.py,sha256=yPIKjgOsG8al3TkceA7uoe9N24cFTM075sV8WVY_b9Q,1624
scipy/signal/tests/test_savitzky_golay.py,sha256=RugNA3h0fOGm2e9l2xhn7Fx83gKXnuPLkA0f4o_FSGg,12865
scipy/signal/tests/test_short_time_fft.py,sha256=j6LArplMsJkj9tCCp8kdf8XswDlPfOHnnTrUDuKDBko,37242
scipy/signal/tests/test_signaltools.py,sha256=3gXHx1W9dZEXU9LmDw8yW9FzvkySW-tgNd0u2PI9_Ro,157401
scipy/signal/tests/test_spectral.py,sha256=JzNzPgHZhDVJh_9VRoA_arO1h5kvFpaGTpwJd7P9o04,80758
scipy/signal/tests/test_splines.py,sha256=xtYPLJRKgG3hB8gOy4-ZVfbITfGeAFHN9z5MbyDeQwk,15067
scipy/signal/tests/test_upfirdn.py,sha256=YfIWWuO9hvSkF-4wny3Usob1TonptW3Gyh8nfU7_nFE,11611
scipy/signal/tests/test_waveforms.py,sha256=Tqu5HcsKw2qOstCYDRiqAmBbZ3iVGeXZ6rYpHkBbbBI,13365
scipy/signal/tests/test_wavelets.py,sha256=JYyICY7OMUBkmQ2GMzReqeXqqfM960q9rsDat_R2pgw,2204
scipy/signal/tests/test_windows.py,sha256=cJorUXsROC9YRfY84uYN7BvbiM7NXciYuT3RPf3VBUE,41836
scipy/signal/waveforms.py,sha256=GIzJ60y5sd1PeYWCEHRJeyqb_EKLLKojXjluMM7juvI,619
scipy/signal/wavelets.py,sha256=RxHaW1yBGGnrA6sMHc9ryMTnf8Q0IMvDuPAgqaxBe7g,527
scipy/signal/windows/__init__.py,sha256=G97xwCUcxrUmZO1pb3K_VfzlyUrzcJYpGjMN9UWWC2k,2171
scipy/signal/windows/__pycache__/__init__.cpython-311.pyc,,
scipy/signal/windows/__pycache__/_windows.cpython-311.pyc,,
scipy/signal/windows/__pycache__/windows.cpython-311.pyc,,
scipy/signal/windows/_windows.py,sha256=mQByF31o0LZh__AKMvcRTt9pVCwVvPHqI3XK_VB7vjA,85968
scipy/signal/windows/windows.py,sha256=7yfP6swdGZdTI7N37_Gg8PWQz0-HyYqMkfWqAHwiVTg,862
scipy/sparse/__init__.py,sha256=TM3iehRpVeZvb2mfzf4zJNL_srM0Jb8HOtsHmlfZp5A,9692
scipy/sparse/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/__pycache__/_base.cpython-311.pyc,,
scipy/sparse/__pycache__/_bsr.cpython-311.pyc,,
scipy/sparse/__pycache__/_compressed.cpython-311.pyc,,
scipy/sparse/__pycache__/_construct.cpython-311.pyc,,
scipy/sparse/__pycache__/_coo.cpython-311.pyc,,
scipy/sparse/__pycache__/_csc.cpython-311.pyc,,
scipy/sparse/__pycache__/_csr.cpython-311.pyc,,
scipy/sparse/__pycache__/_data.cpython-311.pyc,,
scipy/sparse/__pycache__/_dia.cpython-311.pyc,,
scipy/sparse/__pycache__/_dok.cpython-311.pyc,,
scipy/sparse/__pycache__/_extract.cpython-311.pyc,,
scipy/sparse/__pycache__/_index.cpython-311.pyc,,
scipy/sparse/__pycache__/_lil.cpython-311.pyc,,
scipy/sparse/__pycache__/_matrix.cpython-311.pyc,,
scipy/sparse/__pycache__/_matrix_io.cpython-311.pyc,,
scipy/sparse/__pycache__/_spfuncs.cpython-311.pyc,,
scipy/sparse/__pycache__/_sputils.cpython-311.pyc,,
scipy/sparse/__pycache__/base.cpython-311.pyc,,
scipy/sparse/__pycache__/bsr.cpython-311.pyc,,
scipy/sparse/__pycache__/compressed.cpython-311.pyc,,
scipy/sparse/__pycache__/construct.cpython-311.pyc,,
scipy/sparse/__pycache__/coo.cpython-311.pyc,,
scipy/sparse/__pycache__/csc.cpython-311.pyc,,
scipy/sparse/__pycache__/csr.cpython-311.pyc,,
scipy/sparse/__pycache__/data.cpython-311.pyc,,
scipy/sparse/__pycache__/dia.cpython-311.pyc,,
scipy/sparse/__pycache__/dok.cpython-311.pyc,,
scipy/sparse/__pycache__/extract.cpython-311.pyc,,
scipy/sparse/__pycache__/lil.cpython-311.pyc,,
scipy/sparse/__pycache__/sparsetools.cpython-311.pyc,,
scipy/sparse/__pycache__/spfuncs.cpython-311.pyc,,
scipy/sparse/__pycache__/sputils.cpython-311.pyc,,
scipy/sparse/_base.py,sha256=xNt88bnMmwps9EAksRSq7iQRGnTES9G4BMRGB7bt8Po,50253
scipy/sparse/_bsr.py,sha256=yvFmTOjS05YYgCQxDJGwkoaVKZIp7O6neNrXIgyk4cM,31811
scipy/sparse/_compressed.py,sha256=xP42R6IaL_pBFXfyFmg0qM-F2FFhKJA8fn32yfM4Mrw,60363
scipy/sparse/_construct.py,sha256=sk_B45DCH54cYlvAakj2h0Q80TNjT-McLipqaliehOc,49362
scipy/sparse/_coo.py,sha256=ktGrfklTqvROj1piRlYuIYasaJ0vmXqcQL9nHzDQ9Fg,65871
scipy/sparse/_csc.py,sha256=grOPRTxFtF6OoBu1EHw87SvrzpypVM464am3gBhzV_4,11509
scipy/sparse/_csparsetools.cp311-win_amd64.dll.a,sha256=Q2XpIA9Lt2KAn4sKLVFIJzrl2XBDt9eROoZuxVhTW28,1616
scipy/sparse/_csparsetools.cp311-win_amd64.pyd,sha256=RuPS5EenZA9tDADDf8sHWG8tnzXttXCSvf6Dd42BhTs,775168
scipy/sparse/_csr.py,sha256=H5BUgBq1s5F-TXm2WsvzhOj-AqqHKd-s1FO-xTO3brI,18714
scipy/sparse/_data.py,sha256=HH6Hiavz3iF1-6yscsxWCVEF8ZAL7vcb2XSc_gv6fQU,21057
scipy/sparse/_dia.py,sha256=4PQqEcDTzNMNveHoxEiHGA2xKyUvmyVx96IefcBwjV0,20657
scipy/sparse/_dok.py,sha256=6rrKVIzB6R_eb_HAsQRiw7sEzOD7svAzdxJ2I19prAw,23703
scipy/sparse/_extract.py,sha256=9_Hbn7ytKL_Sn8ZT1ElWU1fgvLNf1jxa2BJGK_3sVDQ,5236
scipy/sparse/_index.py,sha256=tay4NFVQLJ5Nq-lfFOTAJTNzMTpur6_lgc7lj8HzACk,16820
scipy/sparse/_lil.py,sha256=B2m0jN98nzHdO61-znne1yEzlUoL44HKM3U7YcsVZa0,21757
scipy/sparse/_matrix.py,sha256=ZnEWvM8gC96QMeyN9dVjsxAownjH_QU5VH50jY3eOUQ,4573
scipy/sparse/_matrix_io.py,sha256=Wr98d8p_VaifuZKm8RNQAFOgHoGmI7DAfr82BxV9uME,6127
scipy/sparse/_sparsetools.cp311-win_amd64.dll.a,sha256=MNFQFVCtURH998-qb4uV8EfR04xVz7p02oVQdjdFa-8,1608
scipy/sparse/_sparsetools.cp311-win_amd64.pyd,sha256=uiTHKIUEe1XwVCJroPuSQdStIgV8gmDDudvuRYQv9j0,4275712
scipy/sparse/_spfuncs.py,sha256=vwBEj6xNGh1V17dONJX76wCAj9iFfmteGqh48DZ_vng,2063
scipy/sparse/_sputils.py,sha256=eb4otiUJWWZc37kXgfLt7yr6GJ6GHIQM_UCXu6hAais,21367
scipy/sparse/base.py,sha256=JL80rDo0TwYloUa1n4tyLRxb_e6lp2ermCU4f0YkG4k,824
scipy/sparse/bsr.py,sha256=QSzbgv-z06WX8-uPsUnkow_tI2SKMjzbaIp_EWXLmvU,847
scipy/sparse/compressed.py,sha256=wx4UDp1GEYdVvdxZg7HdnAVhCYhfWb1Sg4zZICB9rbo,1052
scipy/sparse/construct.py,sha256=expKWVHfVSTs_809lRGCyJM1zm9urS2cECQdPiqmgAo,969
scipy/sparse/coo.py,sha256=irEGMbdj__vcKOQOcqARXS-exo17SFl7cplcmOOQ1qc,881
scipy/sparse/csc.py,sha256=2zsFtNCw5agBC2g8U-AUtB_PbQfVrPgPhzUSQ2bpA_A,634
scipy/sparse/csgraph/__init__.py,sha256=ErtX2spmCtvJdiVYtH19oi3mw8TSK4lHsnE4RleCODU,8052
scipy/sparse/csgraph/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/csgraph/__pycache__/_laplacian.cpython-311.pyc,,
scipy/sparse/csgraph/__pycache__/_validation.cpython-311.pyc,,
scipy/sparse/csgraph/_flow.cp311-win_amd64.dll.a,sha256=N318yQ4iLUX6bA5T0b2ckeym1XDzG5xH-8r1CHFioas,1520
scipy/sparse/csgraph/_flow.cp311-win_amd64.pyd,sha256=o7gB5ODZ0PxhMBSGf_zsyDjaHlW4dBX0bhVpEMLipwo,315904
scipy/sparse/csgraph/_laplacian.py,sha256=-qgkmuqfOsQfMjF2mvjpXJZ4SFOl71_Mle_6km6Rmag,18836
scipy/sparse/csgraph/_matching.cp311-win_amd64.dll.a,sha256=SGzkUpZNQWQ67SKkd9q9c0T5K7eTZnhqGLZb518A598,1568
scipy/sparse/csgraph/_matching.cp311-win_amd64.pyd,sha256=td7s56FTfDcIaReL-KqgUNfQ3wSv1VzsUACp4vNWZN4,321024
scipy/sparse/csgraph/_min_spanning_tree.cp311-win_amd64.dll.a,sha256=CXvtK-J0EvspM_jG9crTAr6ZI-EEL1K06ckhfVLSpiA,1676
scipy/sparse/csgraph/_min_spanning_tree.cp311-win_amd64.pyd,sha256=G_uBNXXwEkfjvvX6wslx5tKZ73t3u9Go_XXTrEUwYzo,244736
scipy/sparse/csgraph/_reordering.cp311-win_amd64.dll.a,sha256=NTgEOezXOkv8_biP8lcU9aWswUb48NWBU-q5iywfZtk,1592
scipy/sparse/csgraph/_reordering.cp311-win_amd64.pyd,sha256=UtR2rz-gxZMWhzjol5cIU1KNT5tNsEh0OL1GBll5YEE,300032
scipy/sparse/csgraph/_shortest_path.cp311-win_amd64.dll.a,sha256=1uUYV036ZSOqQRAtw7kfCvG2cOLMuXzaVvmuUREZS0k,1628
scipy/sparse/csgraph/_shortest_path.cp311-win_amd64.pyd,sha256=N-XCpeXj2ZxJ-jr-NnoAS05nvLOfMeVu5zRIK3PyyI0,534016
scipy/sparse/csgraph/_tools.cp311-win_amd64.dll.a,sha256=O0BxyAy5uuanKFDB3-UEJryW2uI9E15xxmGlAuvkt6A,1532
scipy/sparse/csgraph/_tools.cp311-win_amd64.pyd,sha256=3yl6jA3RCVRt27ePlQ9GAyLZdt0M7OaY4-ds_0IIv9Q,185856
scipy/sparse/csgraph/_traversal.cp311-win_amd64.dll.a,sha256=Nz9f-HJpKvmAFFrTXUEb1CYKr7eNTCwT6p3syl1dWUw,1580
scipy/sparse/csgraph/_traversal.cp311-win_amd64.pyd,sha256=ques7XBbugb2PxWZ0LoBPsPOZJ4mGMrHaCmPcqr2ijk,611840
scipy/sparse/csgraph/_validation.py,sha256=TupvEfqEDYz97SMxP_EO658lWBJH31G3W6ViP7fZIJI,2695
scipy/sparse/csgraph/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/csgraph/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_connected_components.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_conversions.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_flow.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_graph_laplacian.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_matching.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_pydata_sparse.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_reordering.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_shortest_path.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_spanning_tree.cpython-311.pyc,,
scipy/sparse/csgraph/tests/__pycache__/test_traversal.cpython-311.pyc,,
scipy/sparse/csgraph/tests/test_connected_components.py,sha256=HiW9gS_ttLPBl9XvdVCRZGRKfWdoiW4PsmVTqp1Q-kQ,4067
scipy/sparse/csgraph/tests/test_conversions.py,sha256=IxS7e33rMTMiBQDBvsT0-BEgQTWHSxi4Ei20aZggUpI,1915
scipy/sparse/csgraph/tests/test_flow.py,sha256=vEXctFogFpSrSm_NMh4XqamdrrzdhZxXvW_Zw8Z2LSI,7927
scipy/sparse/csgraph/tests/test_graph_laplacian.py,sha256=N9WMTrys6E4FHisuuVziu-ofW7wzgIxl4jhOu5dIMZs,11304
scipy/sparse/csgraph/tests/test_matching.py,sha256=KSlkoEvAszFXzkL9F1wv9-7vQxWVaBGiXOji1epAmn0,12166
scipy/sparse/csgraph/tests/test_pydata_sparse.py,sha256=sjkf2L-sbVDPb5_4uiSlOgMkPjrLrLZVgLoKMTQY4Xw,5063
scipy/sparse/csgraph/tests/test_reordering.py,sha256=78ZmoeqkNB4a6zyFV37IAo-f-R48gPe8G9g8mF-spls,2639
scipy/sparse/csgraph/tests/test_shortest_path.py,sha256=BTOKrBkJsPEtv1l1xpuMEKVLFsKP60HskMxMxrTV5HM,17059
scipy/sparse/csgraph/tests/test_spanning_tree.py,sha256=vOPVZpD7U8Whhint2LmT-vejC-RduRaVCzqX2X-r1mU,2230
scipy/sparse/csgraph/tests/test_traversal.py,sha256=M_C0cRNv3VSbb8uuFKqOODBCfBNs5wf26Nri59UldZg,6297
scipy/sparse/csr.py,sha256=pVwLe3hgplNxZCjDixenMe2_UhHwHnNm_uNTb8X1Ytw,685
scipy/sparse/data.py,sha256=jBxVtluwVoejuNTf-QYgAYw3DdQpJiXM4WfgMUd_SV0,596
scipy/sparse/dia.py,sha256=FI2MAFv_1H0zFa6rrjdE-UZC-TBilpKVdGAspwpL0yY,718
scipy/sparse/dok.py,sha256=GdVpJOh-A6vtT3hR-73ziDU1-V0PFdPVUAfqeTTvUTo,765
scipy/sparse/extract.py,sha256=tcwXkf3fvxKg4PotXMrvrjeMzCgqvUQBbDBzrKdLEJo,590
scipy/sparse/lil.py,sha256=mudAFHN3Tk2eQS1cWTQRAQAXGuPnikOtYWNWYRvwQqs,584
scipy/sparse/linalg/__init__.py,sha256=9nVFnMPzPtn1K0uhxyJoG9JP25kj0FVrcSkU9czesfI,4259
scipy/sparse/linalg/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_expm_multiply.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_interface.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_matfuncs.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_norm.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_onenormest.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_special_sparse_arrays.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/_svdp.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/dsolve.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/eigen.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/interface.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/isolve.cpython-311.pyc,,
scipy/sparse/linalg/__pycache__/matfuncs.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/__init__.py,sha256=Bb2z-gERMui5XZRbT6tOjUJbTLk1v_l2hLFWR7yyJYM,2110
scipy/sparse/linalg/_dsolve/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/_add_newdocs.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/__pycache__/linsolve.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/_add_newdocs.py,sha256=2rzAsN26FhpV4gxSyCeN1YXb3meVm_cx5bx48XNh1p4,3890
scipy/sparse/linalg/_dsolve/_superlu.cp311-win_amd64.dll.a,sha256=n87EGYghtTJ7Hueyq3xR1Zaq2qovO2LmcPxBQozlryY,1560
scipy/sparse/linalg/_dsolve/_superlu.cp311-win_amd64.pyd,sha256=MOjtA2d5h-4DXV9xXPl9EhOb-fVC5VQZ8BA9CeeyqQY,541184
scipy/sparse/linalg/_dsolve/linsolve.py,sha256=YWq0QqlszOpbCboe1woPEYwtdxpbchUx57nllpZwDM8,31570
scipy/sparse/linalg/_dsolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_dsolve/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/tests/__pycache__/test_linsolve.cpython-311.pyc,,
scipy/sparse/linalg/_dsolve/tests/test_linsolve.py,sha256=JjWcmMBEVAzqrzdE4RL3yk-g9ILeIKa23tJ6HRt09Ms,33814
scipy/sparse/linalg/_eigen/__init__.py,sha256=013F3u6pMe0J0wKjMFq_a1jSuO-pwS_h_XzmJI3xfMM,482
scipy/sparse/linalg/_eigen/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/__pycache__/_svds_doc.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/_svds.py,sha256=Qm7n8nD_ZBwNkURq6rv6i9Ui96tVi8Re-O0i_aMpYNQ,20448
scipy/sparse/linalg/_eigen/_svds_doc.py,sha256=QXVUkudMCA3Br7Tgg1oLqdz4SGbxudZuiGt79dmWw9U,15385
scipy/sparse/linalg/_eigen/arpack/COPYING,sha256=_LPGx94UYM99CGPDxZluUY64AVouztNpEfPaF4RAs98,1937
scipy/sparse/linalg/_eigen/arpack/__init__.py,sha256=EU0vXuTlZzMcXxCITUTVxUZ_N0EgcDF06Q2rMzy0Q3o,582
scipy/sparse/linalg/_eigen/arpack/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/__pycache__/arpack.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/_arpack.cp311-win_amd64.dll.a,sha256=Nkd5ItKKocBRk10AAQbpPfzzS90bK3NoY4-G_y6pnPM,1544
scipy/sparse/linalg/_eigen/arpack/_arpack.cp311-win_amd64.pyd,sha256=ljF92fZfHuVevFf_8iBQIbCAYwJ1LvMiDftKSHeAXdE,922624
scipy/sparse/linalg/_eigen/arpack/arpack.py,sha256=LjMP23lxrdS5upSWJrJUJ_rnRgfu5AGx8--c0cPxjBI,68869
scipy/sparse/linalg/_eigen/arpack/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/__pycache__/test_arpack.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/arpack/tests/test_arpack.py,sha256=lbDDNeiT_FPNGptjaYuJ--0dfA6OqIcjeL8dYP3pWLg,24452
scipy/sparse/linalg/_eigen/lobpcg/__init__.py,sha256=8aw6542gPHNriFRBENTZ5rb1M3cqJKToG--paaqpaxM,436
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/__pycache__/lobpcg.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/lobpcg.py,sha256=JhxmvYgV16pvHRy7ZURZoLsIBrHXsnhyNirmU-jZSIQ,43061
scipy/sparse/linalg/_eigen/lobpcg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/__pycache__/test_lobpcg.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/lobpcg/tests/test_lobpcg.py,sha256=AgCm_YIFsCP2bqJN9inRJJddPaKR5iIbw_WCiPn5vHM,28146
scipy/sparse/linalg/_eigen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_eigen/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/tests/__pycache__/test_svds.cpython-311.pyc,,
scipy/sparse/linalg/_eigen/tests/test_svds.py,sha256=XVjdEe584u65fLWGDnSW5JFBQ1_pP49qBayvAETvR-o,37680
scipy/sparse/linalg/_expm_multiply.py,sha256=b8eIKs9PHlSN0jxdtsjohPL4jeRIVM7B_xc-JmdgKk8,27307
scipy/sparse/linalg/_interface.py,sha256=c-aDaAhWaJPaJNV6CR4RjmSNY16LqC9_cGQsIhKCEj4,30341
scipy/sparse/linalg/_isolve/__init__.py,sha256=rFcoMYM70Tbtjlc3xhWhgjdNHUOwc4TSV1sOFZwL6Gw,499
scipy/sparse/linalg/_isolve/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/_gcrotmk.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/iterative.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lgmres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsmr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/lsqr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/minres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/tfqmr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/__pycache__/utils.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/_gcrotmk.py,sha256=1JgKRjIc61-Lq9EyX39EvYRVAfumQg5cZ-adH7MdVto,16291
scipy/sparse/linalg/_isolve/iterative.py,sha256=XG8CpqhnCZbq_9nyeXdrBMucqPVROB6mJIGg_dKBZa4,34906
scipy/sparse/linalg/_isolve/lgmres.py,sha256=zuWUA5DGRsn924xvUhpMccmGMO7x_cCa-99FmkWMdpw,8925
scipy/sparse/linalg/_isolve/lsmr.py,sha256=8eYL4_Uhb2Ar8zU1rMckdKqA--AUnVkpFF0EMrwp4M4,16136
scipy/sparse/linalg/_isolve/lsqr.py,sha256=UzxSqzKZi77rfa8a5PGvjd0slyshLbRaEQQ1-6gQ6NE,21911
scipy/sparse/linalg/_isolve/minres.py,sha256=ffilumTyGarv0GZpN4q0Ablj7JlvTtCNEEpD8ytygP0,11259
scipy/sparse/linalg/_isolve/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/_isolve/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_gcrotmk.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_iterative.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lgmres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsmr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_lsqr.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_minres.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/__pycache__/test_utils.cpython-311.pyc,,
scipy/sparse/linalg/_isolve/tests/test_gcrotmk.py,sha256=N0gqodK3NSLtjdeBsBbVQps-SsdAtxBhyxPYK2N8mWw,6044
scipy/sparse/linalg/_isolve/tests/test_iterative.py,sha256=7NkhXoHl5cmuOxM2oP2JZ-LxM9d5zZnDrVaUxPo4kW4,26990
scipy/sparse/linalg/_isolve/tests/test_lgmres.py,sha256=65KcnvybKHqmcJVXVTvQjzcEXMQ5C_2W9vGJAxRkWOo,7673
scipy/sparse/linalg/_isolve/tests/test_lsmr.py,sha256=jEw11X6oBX6fObpWX2ZTMNzCiIwxlVRHPuK9m1XDl6c,6547
scipy/sparse/linalg/_isolve/tests/test_lsqr.py,sha256=i9aWA_Lxjz4N8TDbQ1TZAZAfDL4CKcmW1g3ktvRMkFQ,3879
scipy/sparse/linalg/_isolve/tests/test_minres.py,sha256=tom4nzeIpvusT1NhxZODOkZ25jqRfsgAmd0rWDXSg6s,2531
scipy/sparse/linalg/_isolve/tests/test_utils.py,sha256=W_ERpPV4ZfxThvgBHxuyhiTBmmfSbQKFFZaSK25mGBg,274
scipy/sparse/linalg/_isolve/tfqmr.py,sha256=EsBS5TjO3Qo0OZtj1OUE88DEM7_3z-GrbBT20Zw5fus,6419
scipy/sparse/linalg/_isolve/utils.py,sha256=LjktcTFezfc_dtnhxbzOfGB-TZhsjTxx_CDVLDA4MZE,3725
scipy/sparse/linalg/_matfuncs.py,sha256=vjpRP2EtMNyFicEbeFDy-lqXgFvXfKg_xp7EVF07ukM,30278
scipy/sparse/linalg/_norm.py,sha256=UlmtIx4YKB1OuS0YjYJtTWzc-igfvWPj0chLuAaEOI0,6358
scipy/sparse/linalg/_onenormest.py,sha256=y1HJKNEm2nZCKw-cNDoquk53NZoF1u1PWlN8SB70DDo,15947
scipy/sparse/linalg/_propack/_cpropack.cp311-win_amd64.dll.a,sha256=-sqQDPSlxnRHSSKvF7PEFhsgwo_OSDl-iZmqM4doy7k,1568
scipy/sparse/linalg/_propack/_cpropack.cp311-win_amd64.pyd,sha256=OuOW3FKaVO-o2IpnwGZKHUqnxVSM-76EYU_KNy0PgjU,604160
scipy/sparse/linalg/_propack/_dpropack.cp311-win_amd64.dll.a,sha256=VILaGm3v3DG6cpgjTI8IzMxnLaLjvzbzdlxB1DzI8Rk,1568
scipy/sparse/linalg/_propack/_dpropack.cp311-win_amd64.pyd,sha256=HbK6pf2PdxOdwGCTHNhzDBMfXmrTBdCil9Yb54DRjRQ,572928
scipy/sparse/linalg/_propack/_spropack.cp311-win_amd64.dll.a,sha256=0AYYqZkf94VAgUzEpXv8Hg2p1foaXwcQ756APsfddNs,1568
scipy/sparse/linalg/_propack/_spropack.cp311-win_amd64.pyd,sha256=9EaRSsLeavmpSAeo-z352DcyXHhAjbJeTAvsnBBlOBo,574464
scipy/sparse/linalg/_propack/_zpropack.cp311-win_amd64.dll.a,sha256=rIJ0k4iJXSZS_TanypiWNe5yUtnbtCtD_Kds0B3KAxI,1568
scipy/sparse/linalg/_propack/_zpropack.cp311-win_amd64.pyd,sha256=HwWyVcxdAXj21z5rCPSFZ9UtxGiMgKGYTgEXN_rMjoo,593920
scipy/sparse/linalg/_special_sparse_arrays.py,sha256=BIk15QfPjD8hpwCTNQA_Hd2bk2Tgw6-A1zy0sqNJz48,35173
scipy/sparse/linalg/_svdp.py,sha256=JuOD5fJ0nTGzCMYeQCBLcHGjOHX0tAE7FlirtqY0U-w,11509
scipy/sparse/linalg/dsolve.py,sha256=FqCBojSMWKfXKL8U2Kt-uhTQjy2bFRK4jdm-0uvmcLQ,676
scipy/sparse/linalg/eigen.py,sha256=RdD87vomwamZfWiKVcxXX7lgI0on79M2K_jzU9wmr7k,647
scipy/sparse/linalg/interface.py,sha256=pUQ396Go80oD5Nhnw1ORA0ug3ilOEJ00tar6czQ4JVw,593
scipy/sparse/linalg/isolve.py,sha256=ra9gIhO5qfFPMKsW8OgQSf4QepdykZpTUMaXXLakEOY,671
scipy/sparse/linalg/matfuncs.py,sha256=414icIriQbA2Mwro2TI1MMkStV6F1fRi9Hhs9mrpsBk,588
scipy/sparse/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/linalg/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_expm_multiply.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_interface.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_matfuncs.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_norm.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_onenormest.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_propack.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_pydata_sparse.cpython-311.pyc,,
scipy/sparse/linalg/tests/__pycache__/test_special_sparse_arrays.cpython-311.pyc,,
scipy/sparse/linalg/tests/propack_test_data.npz,sha256=v-NNmpI1Pgj0APODcTblU6jpHUQRhpE9ObWb-KYnu6M,600350
scipy/sparse/linalg/tests/test_expm_multiply.py,sha256=btt1PXGw7k0X12ksf4nBbbajIOi_DPpoDeQvZvoY9tc,15212
scipy/sparse/linalg/tests/test_interface.py,sha256=oBESVRYjWxRhk3s0kjbshA_XD-sVzAYoctkCnD7UOVs,21647
scipy/sparse/linalg/tests/test_matfuncs.py,sha256=TCkyKYiOw9JSrzVcrreDz7fDZIpzU2l2N7kg92nZL58,22332
scipy/sparse/linalg/tests/test_norm.py,sha256=ssry1tYOW3Rf0OnwWSV4PYoAi6ANm6TAq12yLmAlR_k,6870
scipy/sparse/linalg/tests/test_onenormest.py,sha256=eJtPniHhjmtVcq_f53k6orimucPJ422vbEi5FocRigs,9504
scipy/sparse/linalg/tests/test_propack.py,sha256=QVfGQXgAQoo83yNvc2YMfrpp3zLC3P7x2RN9p4gYFwk,5732
scipy/sparse/linalg/tests/test_pydata_sparse.py,sha256=npBfywlJuB8seqMC_gq_Pe-BxzdBVK_LMo9MYJZOz1s,7067
scipy/sparse/linalg/tests/test_special_sparse_arrays.py,sha256=a3CKlj_vI_A45sZgx-36usRCswvQNeutrX0hRK9bkzk,13191
scipy/sparse/sparsetools.py,sha256=sKV6diNaH4r4B1BV-F-1AvuGAHw2IpyraSJlBoXZ9YY,533
scipy/sparse/spfuncs.py,sha256=FK1cecfhjHLk2_gSAoTSsved5HgcOwUPrtNSnG7f2_o,525
scipy/sparse/sputils.py,sha256=ckuUej4jR09MrvynJTZ9c6_QCKTHn-7ZUeBcBf6MUzs,525
scipy/sparse/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/sparse/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_arithmetic1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_array_api.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_base.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_common1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_construct.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_coo.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_csc.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_csr.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_dok.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_extract.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_indexing1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_matrix_io.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_minmax1d.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_sparsetools.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_spfuncs.cpython-311.pyc,,
scipy/sparse/tests/__pycache__/test_sputils.cpython-311.pyc,,
scipy/sparse/tests/data/csc_py2.npz,sha256=usJ_Gj6x_dEC2uObfdYc6D6C8JY4jjROFChQcZhNAfo,846
scipy/sparse/tests/data/csc_py3.npz,sha256=axuEMVxwd0F-cgUS0IalpiF8KHW4GNJ3BK6bcjfGnf4,851
scipy/sparse/tests/test_arithmetic1d.py,sha256=uBDTwReklS7TR2AIZPFvniTcA-7-N0nZvaSNEwiIvD0,12213
scipy/sparse/tests/test_array_api.py,sha256=U56C4NdY9P3HVoxbS57IR-M4l8U-fZluYPIcIRV_Dfw,14762
scipy/sparse/tests/test_base.py,sha256=YY7jkmBvavQzd84U2UPZPj__8YdwEPeMFq-L1cT8jMc,219433
scipy/sparse/tests/test_common1d.py,sha256=Q9Wy4ENHAaEx9xtgrEzD8a6MW_toP-OUHDnuN36BGzQ,15951
scipy/sparse/tests/test_construct.py,sha256=JQREhFZfjdIqXenXfxkOo7jIod8TKr2d8h-G9ISVepc,39305
scipy/sparse/tests/test_coo.py,sha256=sXCGhodrGmbgSBZZ8l9M6cXomaJc30Xpf59I6liFTSQ,29985
scipy/sparse/tests/test_csc.py,sha256=ak1_Ka7itovqPa9NltnRgnMx_yvjNNw6sFsPaISZZyU,3056
scipy/sparse/tests/test_csr.py,sha256=WoddbHT0VvwQ7tB3XxDCfGFiL4Bo89vXoOP2YXmyYzc,7837
scipy/sparse/tests/test_dok.py,sha256=cpmX7WKvbFIhO41I1tNcNPm9GC1ToFCz-SXvDUPg_sM,6168
scipy/sparse/tests/test_extract.py,sha256=NJIEcflkpb-k0llvH_6uJMAoaysWliWyi4bRjl0NhN8,1736
scipy/sparse/tests/test_indexing1d.py,sha256=cDL0drcXj7TsCaSam0Wbw4F51LBq1t1QX8KdiFikQ-E,21357
scipy/sparse/tests/test_matrix_io.py,sha256=7SOFHH8giia5Xdm5QmNuu1Sr5rLYtGF916iQYPqIsJU,3414
scipy/sparse/tests/test_minmax1d.py,sha256=Ceacg9C5H_qMXEsiLrxpwzUeDUyhivEYCKlNGANwk7Y,4397
scipy/sparse/tests/test_sparsetools.py,sha256=5qjHLoIkDjWyiSTJQ0aXK9srBn9ysK2rJkGny5o1FZs,10882
scipy/sparse/tests/test_spfuncs.py,sha256=jUPhJjGat0YjcEeZWrD6Iz2G3el0ubAyts4ezvbrCM8,3355
scipy/sparse/tests/test_sputils.py,sha256=ifwDm10bwM__mTux8UXJKgxgYrdT2GR67nWYvIVmN-k,15478
scipy/spatial/__init__.py,sha256=poHN9MOFpQpE9zL8GT-iYH6TrV_yf5qgzQASLhz2rFw,3860
scipy/spatial/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/__pycache__/_geometric_slerp.cpython-311.pyc,,
scipy/spatial/__pycache__/_kdtree.cpython-311.pyc,,
scipy/spatial/__pycache__/_plotutils.cpython-311.pyc,,
scipy/spatial/__pycache__/_procrustes.cpython-311.pyc,,
scipy/spatial/__pycache__/_spherical_voronoi.cpython-311.pyc,,
scipy/spatial/__pycache__/ckdtree.cpython-311.pyc,,
scipy/spatial/__pycache__/distance.cpython-311.pyc,,
scipy/spatial/__pycache__/kdtree.cpython-311.pyc,,
scipy/spatial/__pycache__/qhull.cpython-311.pyc,,
scipy/spatial/_ckdtree.cp311-win_amd64.dll.a,sha256=hPxQ_HshlrkuRIoRY6gcqQhibstmb6GxIs8fVue6NNg,1560
scipy/spatial/_ckdtree.cp311-win_amd64.pyd,sha256=oqrGlrVgt8mDy0HG6tCkoKWUO9sgKt50wDaPgUxFZ2I,1697792
scipy/spatial/_distance_pybind.cp311-win_amd64.dll.a,sha256=k6Gy4c-opn8RmAkhtInaev5UKHi917i-JW2BkPxEh2Q,1656
scipy/spatial/_distance_pybind.cp311-win_amd64.pyd,sha256=1LLY4dl7fN8KuJqXnjykGa1WGuNsj4Ii45QRsPMTBrY,1375232
scipy/spatial/_distance_wrap.cp311-win_amd64.dll.a,sha256=-BN1U6HFbP08QyV-YkZZGmh6Lvv7s0HO1NN3fnAokQk,1628
scipy/spatial/_distance_wrap.cp311-win_amd64.pyd,sha256=wnLzthzCPW0Mk4-mC3Rp2MfH6e1Bp_Ay9RGxPDPoNEQ,111104
scipy/spatial/_geometric_slerp.py,sha256=QDtTuk_oZ5t7_v3Qjnzb4pMYmkC_9Yd_-7rNXRsTZBE,8189
scipy/spatial/_hausdorff.cp311-win_amd64.dll.a,sha256=Xn88txqzgwJDA-hvtq7ajfdmrUPtScXKxEilhdjFBnI,1580
scipy/spatial/_hausdorff.cp311-win_amd64.pyd,sha256=Dk9-vW-vUHaV1_259bp5NS-KGNfUjWsGaltVA7D1Ryk,226816
scipy/spatial/_kdtree.py,sha256=KMN1TaeHvhws_c2WBGLjrTMPGSMmcZaoEKjSKwaBhv0,34399
scipy/spatial/_plotutils.py,sha256=jF2xcCqATYsYvcU9mT3TrSWxFa_0wFYMYt1-Q7LXzt4,7831
scipy/spatial/_procrustes.py,sha256=7C9v3o_Any1L7SNCpE71iLJaV8nXUZfpvyEC7Mu4i78,4559
scipy/spatial/_qhull.cp311-win_amd64.dll.a,sha256=tnzVpKtFUd0RSrC2-1ZihssBL5V81Q8d3GhdzCGJP2k,1532
scipy/spatial/_qhull.cp311-win_amd64.pyd,sha256=IgNuvI9sEhGipiAWAhzFJr-Ucfwdpdkg_pqup5fvuzs,1090560
scipy/spatial/_qhull.pyi,sha256=L06jd8pQcSB-DcRMJe8wq0XvlIEzuOrZQAffq4zD7xU,6182
scipy/spatial/_spherical_voronoi.py,sha256=GC2l8QKNKwkg3Zvsi36jaJuEOAYQb16B6z0Gn1kEe_s,13918
scipy/spatial/_voronoi.cp311-win_amd64.dll.a,sha256=gYGzv7tZKBkLp180-Pnk1Yb8SIDwLt6iIuKiS10lrdI,1560
scipy/spatial/_voronoi.cp311-win_amd64.pyd,sha256=65ddoqr8aJ0J734doyhTzLBoRx71I2UzgTQe_iTvuBc,218624
scipy/spatial/_voronoi.pyi,sha256=gaEmdjWgHeIA9-D6tYXwimJCppJzgc6yc-sfLju9Vyc,130
scipy/spatial/ckdtree.py,sha256=Opsyz6WpYiV-27W5GJdfOWQTkQToWndreEx4GnwT2qM,541
scipy/spatial/distance.py,sha256=ZL042yvsqQPSRrCBtyF6ir5YVe3Rkzqs_lFCICKXqUQ,101141
scipy/spatial/distance.pyi,sha256=InigXoZyyknOyRQRs8zs2_7PpSO8fho0abGrWThBhlY,5448
scipy/spatial/kdtree.py,sha256=zc4Kg-a7EvN2hXJq6HuCdPl6ccEbg5GyDV67Xq5nsiA,661
scipy/spatial/qhull.py,sha256=10n1eDcF9qrUmGpqrEb8sLjw_cXfSSgT_WU0-z4ucU4,647
scipy/spatial/qhull_src/COPYING.txt,sha256=liRS5zfffHQ6PcJ0QjIHECi4wEUOdnSlUFfDMOoZd-s,1673
scipy/spatial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test__plotutils.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test__procrustes.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_distance.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_hausdorff.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_kdtree.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_qhull.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_slerp.cpython-311.pyc,,
scipy/spatial/tests/__pycache__/test_spherical_voronoi.cpython-311.pyc,,
scipy/spatial/tests/data/cdist-X1.txt,sha256=LTxfJD5Hzxi0mmMzhzCgp0dUJflrg6O5duj8LVxa9lA,5760
scipy/spatial/tests/data/cdist-X2.txt,sha256=Tb2q8iKbPvUlxO_n1pBfYkWvu1z_LuBcwFXCIRZgBOg,11520
scipy/spatial/tests/data/degenerate_pointset.npz,sha256=BIq8Hd2SS_LU0fIWAVVS7ZQx-emVRvvzgnaO2lh4gXU,22548
scipy/spatial/tests/data/iris.txt,sha256=sWic54AuDE3IyLk6XOw0BcyCSzr5BkXrpDJc7B00OTQ,15150
scipy/spatial/tests/data/pdist-boolean-inp.txt,sha256=D6kMC8OK5H2Z6G07l592VtEENnehwy8wuM1rjVq7wXU,50020
scipy/spatial/tests/data/pdist-chebyshev-ml-iris.txt,sha256=Ysw196_xQ_ApwKiaB7OXZ-gVvhvhFH8wUZAw7EfacTs,178802
scipy/spatial/tests/data/pdist-chebyshev-ml.txt,sha256=jtPmZwg5AfpIom-wMjty8_6tI69ePUnJ7eYdFyFq9G4,3042
scipy/spatial/tests/data/pdist-cityblock-ml-iris.txt,sha256=Wf3dVmhan8EEYiKAsrgN5WNMmAmH9v9NjJpO4xRdmPE,178802
scipy/spatial/tests/data/pdist-cityblock-ml.txt,sha256=aMGSV28cjKIV2CRfdR2zZsisln6oxi1tYQbxZ9tuMMo,3042
scipy/spatial/tests/data/pdist-correlation-ml-iris.txt,sha256=PdVB_30O5CHL9ms8KRB1kFiswp9Sed8CV540WQvNaJc,178802
scipy/spatial/tests/data/pdist-correlation-ml.txt,sha256=VDgXqx3vUn26r1EZFcSk-rq1-wef2WVFcoBWLG8zvro,3042
scipy/spatial/tests/data/pdist-cosine-ml-iris.txt,sha256=q5nrvDyyqdltrjBRyT5eFVQIW-y2Igb2JdC8tBAXl7E,178802
scipy/spatial/tests/data/pdist-cosine-ml.txt,sha256=nLK5yDtcmU-svoYeLPuZZoolZpxmpOaeLswLLR5QoGM,3042
scipy/spatial/tests/data/pdist-double-inp.txt,sha256=2UzMIvz_r7ZT2Twt-jH-pst0Avue4ueGZezk7T2_6q0,50020
scipy/spatial/tests/data/pdist-euclidean-ml-iris.txt,sha256=VWMmzL2jP_vhDzTBhYtZH7-VBKJFpafRROrefDeokWI,178802
scipy/spatial/tests/data/pdist-euclidean-ml.txt,sha256=Kn8vHq17IbWtj1G9XzCFY1EuXFTPnD7gX9FwW2KSm44,3042
scipy/spatial/tests/data/pdist-hamming-ml.txt,sha256=OqjHwrqGBwmwsfTeN2fI1EFiWSpOA6ZlZYwN121Ys1o,3042
scipy/spatial/tests/data/pdist-jaccard-ml.txt,sha256=nj_JYQ6bfvhlFDKu5OC_zBeOzPKgewkmpxScJEPAzhs,3042
scipy/spatial/tests/data/pdist-jensenshannon-ml-iris.txt,sha256=wz1O-EHZT2cDwFtow4ZoLWQgS8WY6DPzb_Ik9SXA8Fs,172739
scipy/spatial/tests/data/pdist-jensenshannon-ml.txt,sha256=rps5AwUK4Up9qCR3wAE1wfwuKE5ftKZTHChPYH0hsfA,2819
scipy/spatial/tests/data/pdist-minkowski-3.2-ml-iris.txt,sha256=rfWuQ4RgSfY0ko_jqlSTjQIJYGCHeEbEwjHYpAC9Ivo,178802
scipy/spatial/tests/data/pdist-minkowski-3.2-ml.txt,sha256=jKlA87ldGof8ndZLkV9l97-Tsh2uXTIlyZRsMoWr0gA,3042
scipy/spatial/tests/data/pdist-minkowski-5.8-ml-iris.txt,sha256=bWYNT7XqpLTGkQBMfnCFJ7bnoxooZlzfI0_O54UOquI,178802
scipy/spatial/tests/data/pdist-seuclidean-ml-iris.txt,sha256=bGIB7ygJC8CGHAoVKtwOdPgAqDM5bzffpgxYMTCKgmQ,178802
scipy/spatial/tests/data/pdist-seuclidean-ml.txt,sha256=g9-qvSA4qe4S0xPxnTa17zqa4Z-P5S0TV1gAsbr-RAs,3042
scipy/spatial/tests/data/pdist-spearman-ml.txt,sha256=vpgBfMlrUUH-nYlnLqm_Ms-MIjrgOlNCW-TW8C4Yan0,3042
scipy/spatial/tests/data/random-bool-data.txt,sha256=sydRG9aL6CH9i7-Nt5X2mNyQDsu9OC1fbUD-MRdt5bc,6100
scipy/spatial/tests/data/random-double-data.txt,sha256=OcmsKJSbi_eY65ld50iAatodSzw2NxFfDJIDHx0lOpQ,75100
scipy/spatial/tests/data/random-int-data.txt,sha256=saHYkK0CMRzV78RV18tUGIF4uKQlOqkN4cEWqD4gnFw,10366
scipy/spatial/tests/data/random-uint-data.txt,sha256=aBbEt1tldXIvAvkDMbSr9WddkxXtMTa05Qq8j4s55CE,8811
scipy/spatial/tests/data/selfdual-4d-polytope.txt,sha256=x-c_sDM8alTwdaj7vYvGAQEsjrg6n5LycGrrPR4dlY0,507
scipy/spatial/tests/test__plotutils.py,sha256=n9OCDDgipsHJy2le-P-8fcS6PD8IQ33zmiz2ZAzbslY,3905
scipy/spatial/tests/test__procrustes.py,sha256=iZypw3jevzOPiHv15UZHwc3sXE3pNlUlCeKTZfoL5vE,5090
scipy/spatial/tests/test_distance.py,sha256=HfpH7vyCNi-69YbXbvSeOllVI6MHSRBEQh4TgDrPxkw,90266
scipy/spatial/tests/test_hausdorff.py,sha256=FThkMuJ8XbVXaGhdAiesq5AZhmnWVsCVuarhNOLsfDE,8416
scipy/spatial/tests/test_kdtree.py,sha256=HZxRVESWrb2WQsJIkuolBxahFbiSXJf4erHRXP5H3-I,50849
scipy/spatial/tests/test_qhull.py,sha256=gMi_vtqSQboIr2TCWc728j_pcpGFF3ovJnlogo-nWxg,51262
scipy/spatial/tests/test_slerp.py,sha256=eS6DagFFbcWqHi3FJJ4zZ4GV7KkvVVf7jkBGbRgOgJ0,16844
scipy/spatial/tests/test_spherical_voronoi.py,sha256=nT88CZzq_W8sPdejyi_2OT8A8b7Yw8qsA7dTZqG61GI,14858
scipy/spatial/transform/__init__.py,sha256=AR19SJ8oEv3Pt2eXGRiHkglp7wU_rGvRJ8JEIBqs4AI,729
scipy/spatial/transform/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/transform/__pycache__/_rotation_groups.cpython-311.pyc,,
scipy/spatial/transform/__pycache__/_rotation_spline.cpython-311.pyc,,
scipy/spatial/transform/__pycache__/rotation.cpython-311.pyc,,
scipy/spatial/transform/_rotation.cp311-win_amd64.dll.a,sha256=BBn2gqf9wpnbXeMqKMDaVwMmd6otNpNpbJU3fxMDcRc,1568
scipy/spatial/transform/_rotation.cp311-win_amd64.pyd,sha256=Uu4MctjXUio30zXZ1sK6Y3241HOS-4CDk-BPwUlsYoU,950272
scipy/spatial/transform/_rotation_groups.py,sha256=XBEazTsMg71VeDaexivk9VYmVDgHPkNji9FrlRypjyc,4562
scipy/spatial/transform/_rotation_spline.py,sha256=weRkpZWCgFeu5q6Ow0Up0k1eJGI1XrApez5VpnrlSYw,14536
scipy/spatial/transform/rotation.py,sha256=ZpBp9OghIvAzY601Zb_9TawwzxRgS9ydRR5aNcboekg,577
scipy/spatial/transform/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/spatial/transform/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation.cpython-311.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_groups.cpython-311.pyc,,
scipy/spatial/transform/tests/__pycache__/test_rotation_spline.cpython-311.pyc,,
scipy/spatial/transform/tests/test_rotation.py,sha256=CftlaSHK8ZXVwDyi2OUv-PC_mLQVaSnZnOY32tf_YXY,72167
scipy/spatial/transform/tests/test_rotation_groups.py,sha256=mATjBt62JCkoqpEMHU2zXB9HF-h7KFpWMBrSLyxnkTA,5729
scipy/spatial/transform/tests/test_rotation_spline.py,sha256=yMBr91x2Tt2GqMvJVTOMcO2n3xgOwGC80MN7HNXQkdM,5267
scipy/special/__init__.pxd,sha256=vZV_tS467FzwvnjYCtD4T8r5NAuPmITjU8ccJioLZ3I,43
scipy/special/__init__.py,sha256=l4NWnMpg7WhLVRoajYHquMNat4xwyewt_SZEkxBXrCU,34613
scipy/special/__pycache__/__init__.cpython-311.pyc,,
scipy/special/__pycache__/_add_newdocs.cpython-311.pyc,,
scipy/special/__pycache__/_basic.cpython-311.pyc,,
scipy/special/__pycache__/_ellip_harm.cpython-311.pyc,,
scipy/special/__pycache__/_input_validation.cpython-311.pyc,,
scipy/special/__pycache__/_lambertw.cpython-311.pyc,,
scipy/special/__pycache__/_logsumexp.cpython-311.pyc,,
scipy/special/__pycache__/_mptestutils.cpython-311.pyc,,
scipy/special/__pycache__/_multiufuncs.cpython-311.pyc,,
scipy/special/__pycache__/_orthogonal.cpython-311.pyc,,
scipy/special/__pycache__/_sf_error.cpython-311.pyc,,
scipy/special/__pycache__/_spfun_stats.cpython-311.pyc,,
scipy/special/__pycache__/_spherical_bessel.cpython-311.pyc,,
scipy/special/__pycache__/_support_alternative_backends.cpython-311.pyc,,
scipy/special/__pycache__/_testutils.cpython-311.pyc,,
scipy/special/__pycache__/add_newdocs.cpython-311.pyc,,
scipy/special/__pycache__/basic.cpython-311.pyc,,
scipy/special/__pycache__/orthogonal.cpython-311.pyc,,
scipy/special/__pycache__/sf_error.cpython-311.pyc,,
scipy/special/__pycache__/specfun.cpython-311.pyc,,
scipy/special/__pycache__/spfun_stats.cpython-311.pyc,,
scipy/special/_add_newdocs.py,sha256=sYtaNupTigbHKJL6rkngZcqy7568OgRlwzj3fdbYqnI,301216
scipy/special/_basic.py,sha256=n_I0s7sY5WbnPuARMrXbDb5BWgV0_h5Yq-VHisLuOVI,115594
scipy/special/_comb.cp311-win_amd64.dll.a,sha256=x2kLhGkyJlSjy9Qq8Bhuqjm-qtH_8b5osZwrWVBlVuk,1520
scipy/special/_comb.cp311-win_amd64.pyd,sha256=Ol5zXzfZTvKcPGgn9rxWnqYTdf2h5i9UMc4jqfRbxVU,49152
scipy/special/_ellip_harm.py,sha256=Km_A9XgXnYTleTtRuUzOYLxR8OEUtmiYJLYsRSJaSNI,5596
scipy/special/_ellip_harm_2.cp311-win_amd64.dll.a,sha256=3uFVd-0YX2iRqO2xkgcOdokiqzxC4aoJ4qa6Cs3Ov2s,1616
scipy/special/_ellip_harm_2.cp311-win_amd64.pyd,sha256=BJ40lyuMAhGSPkk9e5Sy3U800b0jr8z68KNCp9gttgI,109568
scipy/special/_gufuncs.cp311-win_amd64.dll.a,sha256=Wr8-xnQx6P79_PPl9VLI20Hx1hzFjfxoXK0BfPkkHnA,1560
scipy/special/_gufuncs.cp311-win_amd64.pyd,sha256=Gp1hYzKeBf79iMc-ShG8AHeLkjRVfNl_PHD-rvd2idg,1515008
scipy/special/_input_validation.py,sha256=RBX0p23Oizg6dybDSKCJGGdConXDNKyf40vVP-v0vag,491
scipy/special/_lambertw.py,sha256=IYmy0Ymjk-l7T84uHr8_OADgpsUPy1K4F17QGRwWXuE,4111
scipy/special/_logsumexp.py,sha256=aFaHLLecle5fHOxPTMq3tEEqTvHp4bglFBAs2NhIX9I,14378
scipy/special/_mptestutils.py,sha256=LXBqtPgWzXvdCuQj9HKceYaSPw_Ud0Iud-UbxLH1ceI,14894
scipy/special/_multiufuncs.py,sha256=YznE9Mzihzn3wLfO6tguIBGQ6O60VcCIW7uA67JBJL0,19132
scipy/special/_orthogonal.py,sha256=nVn94ENSKI5EoWPTLFzGwYbD0xTMbrvFBMn0DI3KJWM,76822
scipy/special/_orthogonal.pyi,sha256=OOCPLsM2d-HcMVZLjlK-VGUDKZ4kC19t9RTGtIpSgNM,8572
scipy/special/_precompute/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/_precompute/__pycache__/__init__.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/cosine_cdf.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/expn_asy.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/gammainc_asy.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/gammainc_data.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/hyp2f1_data.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/lambertw.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/loggamma.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/struve_convergence.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/utils.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/wright_bessel_data.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/wrightomega.cpython-311.pyc,,
scipy/special/_precompute/__pycache__/zetac.cpython-311.pyc,,
scipy/special/_precompute/cosine_cdf.py,sha256=7Fjlx721WrFcUMSUh6Vv40NMiR1QX71963X1tHOd55M,371
scipy/special/_precompute/expn_asy.py,sha256=trfFT7O7EweO7DfQHIHCzkwWNuqm7sTtCo-HjsUQthY,1319
scipy/special/_precompute/gammainc_asy.py,sha256=8XBo6RBUzdxUhH7GrVeVTzsMJgSnGb9NpknFAdHwcBY,2618
scipy/special/_precompute/gammainc_data.py,sha256=hFFkqjUUSdwgkyulp-p_5LCMav3CHL_aDs_JbcQWl_M,4201
scipy/special/_precompute/hyp2f1_data.py,sha256=rK8obG72p-UmpdjSvhS6-xm4A4TRscsUNAMKOuKZW-Q,15191
scipy/special/_precompute/lambertw.py,sha256=UsSir6v9vfyYrlu5F3W-Qn3MMW5RzqqftfZAC-kTH8I,2029
scipy/special/_precompute/loggamma.py,sha256=J2jz62nBO-7JEOA7nG4JQQt5BuXSfUPRik2r3pwTr_s,1137
scipy/special/_precompute/struve_convergence.py,sha256=f21NuMoJE1Di4MF2kZfd6b_wiwqml-uQPylWdpncK_Q,3755
scipy/special/_precompute/utils.py,sha256=tJpjcNS6WaXzqP7cLac7yFyXfb_pfe3AKvDCa0mwi9A,925
scipy/special/_precompute/wright_bessel.py,sha256=4IHODoYYrAFKw8nlJWAPzrOduiHIMUILTJaqtA4jbq4,13210
scipy/special/_precompute/wright_bessel_data.py,sha256=TIwEyxKLTl2DqbLvppxdVWB1jJu-hL157M3JMjFK6eI,5799
scipy/special/_precompute/wrightomega.py,sha256=hAKXz3Kk9L-5JOBA86U6-yC_tsF8gOz0p8Y1vmWcAzE,996
scipy/special/_precompute/zetac.py,sha256=cZMVkubEXtildiIM9p5ldrV1sEoy240MckKPO3JhM6A,618
scipy/special/_sf_error.py,sha256=hDbymj1eSiPjl828PR-Kidkcd8RiVDpJEB-fGA9ltHM,390
scipy/special/_specfun.cp311-win_amd64.dll.a,sha256=rIYfTEvtC6yKXJAyDGXdeQHsUbF4tALMBUOAJHi654c,1560
scipy/special/_specfun.cp311-win_amd64.pyd,sha256=pc5uNKPOw9f3FFQZQq4_VoerbxggAVeGN_aifObhNkE,346624
scipy/special/_special_ufuncs.cp311-win_amd64.dll.a,sha256=J8fT1R00A-1kOrpQeYP29TO6-XGhtBp3gjopKIlCWnM,1640
scipy/special/_special_ufuncs.cp311-win_amd64.pyd,sha256=XOu0yzPAuD6JLpAoKSoJ2NrvcR-9psRlJ_F2RJUsw4s,2190848
scipy/special/_spfun_stats.py,sha256=Zb72pfmi5hzvkRXD2QXhVnJMmYyIjoqrg6tVxaCcoSM,3885
scipy/special/_spherical_bessel.py,sha256=jikbfmdAfJ1Df6Vr554VHpqcjh9tVeZNmaE_Wp5iFko,12843
scipy/special/_support_alternative_backends.py,sha256=Enco9dQtNhm_mClmjJby2OCNIMzVh99DPsQdAyNBAQg,6517
scipy/special/_test_internal.cp311-win_amd64.dll.a,sha256=JxYXtLE7PkDnehqwLZXooGxZImgOvt0uEhhVV8m5i8k,1628
scipy/special/_test_internal.cp311-win_amd64.pyd,sha256=R9V0rdSntgIfs1dSwoDldwoZhgd-WJ-iJ6z8KGFkl-0,233984
scipy/special/_test_internal.pyi,sha256=fFGF8biY5WYD8O8MlPyUJKS-K3FSuSc1IRv4M8lj7OY,403
scipy/special/_testutils.py,sha256=46gb4jcDatoAbYcJ4zZ_TBDvM4KbmKZG7_8JrnqDh4w,12296
scipy/special/_ufuncs.cp311-win_amd64.dll.a,sha256=e59pgSph1CsDhPqrYAiMN7pajDBTd_B3ssSHYXRvW20,1544
scipy/special/_ufuncs.cp311-win_amd64.pyd,sha256=C4IGLGK4xxL98Fx5TMIRFEPdOHWgr_wu29jzKlb2OmA,1470976
scipy/special/_ufuncs.pyi,sha256=YZBYsqGcpdsUxAd44n-D_FJnKdU4I2DZZiMxW674LJE,9360
scipy/special/_ufuncs.pyx,sha256=LrGiCxxx9Y_8CgRlCzX9YcDXX9TKTFC5sHpqvPpR8mQ,620170
scipy/special/_ufuncs_cxx.cp311-win_amd64.dll.a,sha256=-D2_kJ97UMukq2XnZsIwEPH1xnEg0Dwu-Opw9XSmFWU,1592
scipy/special/_ufuncs_cxx.cp311-win_amd64.pyd,sha256=HaiMiIJ9i3cvy0UHXrSX3FHTv-WbnAXnfuExV_kTc1o,2377728
scipy/special/_ufuncs_cxx.pxd,sha256=zYWbmNfEYk87w3V_k4AfJoqpzcDFQy_LWCA9Kvg8Mes,5798
scipy/special/_ufuncs_cxx.pyx,sha256=8foBle18-UejWI9SJkHK3yY7wQi5VcihcxQVu7BYi2g,31935
scipy/special/_ufuncs_cxx_defs.h,sha256=eWx7afmUlb2GHbqRhWPO84TOrIxx2M827FdbjJ6cggM,9770
scipy/special/_ufuncs_defs.h,sha256=HLWzDicrImbXMmiAXKnNVADs9wjZXKynB3TNlywOpUY,3231
scipy/special/add_newdocs.py,sha256=5i4vyh9UrnCUZ6zTcVmy0YhsbP6z1Mddxb7qrUdWwKE,451
scipy/special/basic.py,sha256=UVJGOHZCijP6H5Kiexi5U2R-CVqfUSCo4Rhv6sxb4bU,1669
scipy/special/cython_special.cp311-win_amd64.dll.a,sha256=0t7DMS9ZKLYGkCMXHIjw6ZLiHxd-KucDQIhAF3Rj5HA,1628
scipy/special/cython_special.cp311-win_amd64.pyd,sha256=qtzXb73KhQ1kev-6k85ZKTb_t7n1TR2oJC73zqEcAEQ,3237376
scipy/special/cython_special.pxd,sha256=FpNS-fk_OOd-d25J2edYV8KixGHV0QRScn8Zq0AL__A,16621
scipy/special/cython_special.pyi,sha256=sC6nE-1ffPYfFKVvopszCin-ilDqBWkDW2O6s0and2M,61
scipy/special/libsf_error_state.dll,sha256=ySFx3LGn73pdEXufm6236BhCBeN9tcfhB4xF3Khd5-o,112594
scipy/special/libsf_error_state.dll.a,sha256=x7JaucLU3Zjarl_Dj_WNjLdxGoDwI48_aRe_WdHO_io,2232
scipy/special/orthogonal.py,sha256=YlmIcqtx79IX4UoUK7FgKuyOLfFCIXCYeFHAvSz4HiM,1769
scipy/special/sf_error.py,sha256=OhEWgiU5kk4QblZIhTAfAc0eU-mUKK_nTA3H-MHlJ68,593
scipy/special/specfun.py,sha256=7-MuCncWBe0xHf_iPWACck2cJ8uR7cyeoMFMD5PtXxw,612
scipy/special/spfun_stats.py,sha256=mLQQYMHf6QowpGShlLy4CDL-Zri1zwyEawpx1U_f51s,552
scipy/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_basic.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_bdtr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_boost_ufuncs.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_boxcox.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cdflib.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cdft_asymptotic.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cephes_intp_cast.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cosine_distr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_cython_special.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_data.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_dd.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_digamma.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ellip_harm.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_erfinv.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_exponential_integrals.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_extending.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_faddeeva.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_gamma.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_gammainc.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_hyp2f1.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_hypergeometric.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_iv_ratio.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_kolmogorov.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_lambertw.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_legendre.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_log_softmax.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_loggamma.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_logit.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_logsumexp.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_mpmath.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_nan_inputs.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ndtr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ndtri_exp.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_orthogonal.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_orthogonal_eval.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_owens_t.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_pcf.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_pdtr.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_powm1.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_precompute_expn_asy.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_precompute_gammainc.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_precompute_utils.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_round.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_sf_error.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_sici.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_specfun.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_spence.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_spfun_stats.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_sph_harm.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_spherical_bessel.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_support_alternative_backends.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_trig.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_ufunc_signatures.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_wright_bessel.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_wrightomega.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_xsf_cuda.cpython-311.pyc,,
scipy/special/tests/__pycache__/test_zeta.cpython-311.pyc,,
scipy/special/tests/_cython_examples/extending.pyx,sha256=FW_hJh5PYqGgpJGy3_J8LZjXf9XLMsMzDu7wbGxiyv8,304
scipy/special/tests/_cython_examples/meson.build,sha256=jAyMIESAHLDCDkdFfJ-kJAOCRtNO6JBD4LdDfE5ZVUU,552
scipy/special/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/special/tests/data/__pycache__/__init__.cpython-311.pyc,,
scipy/special/tests/data/boost.npz,sha256=9W9uqJTP5Z7aJ23carRakXSwFWEt5x_6RDvsI9_rAw8,1270643
scipy/special/tests/data/gsl.npz,sha256=X7Vf4jA3wye4vNm0C6X9KNosK2_NSzPBxGwR1EYmvag,51433
scipy/special/tests/data/local.npz,sha256=ykTwo3nEwxCbDrGbvE7t-kjbmxQ-IJaoNyuXTdwOEFY,203438
scipy/special/tests/test_basic.py,sha256=mtpCgn6Q5khsooM3GOw2weXlXeMoZ-miZMe9f6WpgIA,194504
scipy/special/tests/test_bdtr.py,sha256=ZPWchkB2hVjJ7MALAN9K4QWI8PeGSxcqduER3_myJRk,3343
scipy/special/tests/test_boost_ufuncs.py,sha256=qyMA2wMDWlhShB0PrbvMX5c7evLPL2PYqFzop3X3ujo,2306
scipy/special/tests/test_boxcox.py,sha256=Mi2LR1O_Oc93R-5H7683VxSc22bqHssJgorQv3dDj8k,3239
scipy/special/tests/test_cdflib.py,sha256=Hys-lEIhKU4ywshaJKzokMcwxlRc23Wzb2rkqoW3ih0,24212
scipy/special/tests/test_cdft_asymptotic.py,sha256=dsIl7h0WEtzX7Y3vCZSMEKpwb4EZoHvwL4j3BvRd8Ek,1490
scipy/special/tests/test_cephes_intp_cast.py,sha256=INhKthUJ4kV3FLmbWJJothJ7NHyBZJiRQgfPA81Jm2U,1158
scipy/special/tests/test_cosine_distr.py,sha256=ENyfDI4OabouefeuzVJQO7UsBE3Eaorv39Z4GQoBc7E,2773
scipy/special/tests/test_cython_special.py,sha256=v2z60n9yL_xE0uazXkYgGmPzN9CakgnwkWjdN0J_7dc,19491
scipy/special/tests/test_data.py,sha256=vdJIDHqqt5rKrq7LwjkzSd8YXbm4AH-xWnaS8HkdQ6I,30899
scipy/special/tests/test_dd.py,sha256=KObFRKa8cqUp9VOGBMln1UG1UtMZqSbW9fqFu9yTWKk,1606
scipy/special/tests/test_digamma.py,sha256=vBfs2G9WcIEP8VGpGaCsFlTD5g3XODAP7gsUEmAq6Ho,1427
scipy/special/tests/test_ellip_harm.py,sha256=gUbH1B2dBI2OkMzrAkNMCkuIzHpd3qDsXvpylpp6LCc,9913
scipy/special/tests/test_erfinv.py,sha256=JMvb2hWNQ63uPevk3kAL6KypAtOMJzg8n1G3Om9A15c,3148
scipy/special/tests/test_exponential_integrals.py,sha256=cPxuyehzPSgv_o3Q1Cfb2ON5NNxgBgyAi8dCDILIQe0,3805
scipy/special/tests/test_extending.py,sha256=RmlvXeXppPP_d_pJdH_s5mQWitad7gKApXboha3mxg8,1212
scipy/special/tests/test_faddeeva.py,sha256=2BXe8JR2d_QBMvjX-bmHkP0TDzy4JgZhM6XvSlqO1HA,2653
scipy/special/tests/test_gamma.py,sha256=-u9wzNPSPsJTs_DJL6VZAXXIcIqJsxOeQy__MdRTGxY,270
scipy/special/tests/test_gammainc.py,sha256=Rrv_OnC7U6EtXSvrbGQpcPb7sccWVPIgIAzjs6DBIxM,3951
scipy/special/tests/test_hyp2f1.py,sha256=vqKPNzLfZlS52h2KtZjDR_1iATRR8l0a0EDcHm8hAIo,94825
scipy/special/tests/test_hypergeometric.py,sha256=qFhK0TMP2PkpFLBy8CpKq0fBqIJ8OUhxSjkxZ8rjs_Y,10230
scipy/special/tests/test_iv_ratio.py,sha256=r3ErUCvZqySpM0KlVJcfGBYzTYAPZNByyiOwdKskdOE,10357
scipy/special/tests/test_kolmogorov.py,sha256=WZQxJMHXgp5xC4JmrG-bXvEpzkA9pRXgxYTCAL2Xn_c,19771
scipy/special/tests/test_lambertw.py,sha256=AoYbanXWmx5twRL-HBARQx4RPSfHMPboSEXl0GXwNfQ,4669
scipy/special/tests/test_legendre.py,sha256=vN4ZuIFQHs5tXUDzgdv9I-r8dXk894RXgTflsVg5w8Q,59443
scipy/special/tests/test_log_softmax.py,sha256=0VhKfjYbv5-bV_d4tWyrG6OawlRjCUi_O8mRSgW0Mn8,3524
scipy/special/tests/test_loggamma.py,sha256=SdxYiItvixR4NabOKLyBHu-iEP7R52Rew8N8bZugH2s,2062
scipy/special/tests/test_logit.py,sha256=Fba-Ftq9bMw5YJ7pfE5yK2eERWaaWu4fuHt3lpNyGiM,6665
scipy/special/tests/test_logsumexp.py,sha256=pcyhcV_EZdbvZUkl4NrEC7xT5bCS1YA7m-6jEPVtY7I,13484
scipy/special/tests/test_mpmath.py,sha256=uNatQocg1PfWuM_0RCVpr2UChWeJCEqZnj-Km-wK72g,76063
scipy/special/tests/test_nan_inputs.py,sha256=nZ0r-oR3zcf_P1b9tQzpfE-WVJy1fAVUYw2AAXIfwgY,1923
scipy/special/tests/test_ndtr.py,sha256=n_Y8wG9MYVmZOXPgJoiYjPGlUULdiPoDQFsuR6HzPWg,2757
scipy/special/tests/test_ndtri_exp.py,sha256=b4vFFkhdzIDWNgmKOEy5UjRCYtHsdkoaasRi_L9lzIM,3802
scipy/special/tests/test_orthogonal.py,sha256=6q7Rw6fwN8OKInAFs50sHBosLe3A0WWSDlV0i6J6VjU,33030
scipy/special/tests/test_orthogonal_eval.py,sha256=3NhYYdqkeNnma-BJgqGYGLFOW_Wlk628AgXksbJ0ugg,9846
scipy/special/tests/test_owens_t.py,sha256=sotiVxRrjoQ4lYjbbBx88nQKHEHhNowfJ7KPdIM-vNQ,1845
scipy/special/tests/test_pcf.py,sha256=VTEqviYLsm0DBnuu1Bpyn6rEocf9sZGSyY12ufwxaYo,688
scipy/special/tests/test_pdtr.py,sha256=YpZQ7ssQ5z0yL2lKss4lDooRlz_3n-aY2q4a8aVXXB8,1332
scipy/special/tests/test_powm1.py,sha256=AG5D4ex4jCS97xN7RlyumVc09OUTuczNxS2IYwt9OAg,2341
scipy/special/tests/test_precompute_expn_asy.py,sha256=DFW5CiFXjHNUkWgZrehKUxPNTd09Cllp1nZ0_FU172g,607
scipy/special/tests/test_precompute_gammainc.py,sha256=sY-xFU6MwT-bzjlXXYqu2_spch0XGJO0sZR6pQHMgPs,4567
scipy/special/tests/test_precompute_utils.py,sha256=EWXMuVz5WDUQISPzh87i02u1im9TcLbDBxyk-BO6iZY,1201
scipy/special/tests/test_round.py,sha256=4gZ_fHkWxMOjZ-ApUERxL2cqHvMcpMStvOv9wPSJAU0,529
scipy/special/tests/test_sf_error.py,sha256=x6OCQhag8X4fwnZIkqXP5uBKMJaJ1ETNJvSqCgKN4N0,4349
scipy/special/tests/test_sici.py,sha256=REYntiqC0T4Pb-66ESBmjEmIoTJDbjYs786XNwUkzIw,1263
scipy/special/tests/test_specfun.py,sha256=wnuwcdU4BDRe4xU4S6Ib2_3rUevGsHJSeYvKV5XLOU4,1735
scipy/special/tests/test_spence.py,sha256=r4chXBi_nKK1uSfPse7owNhvcC93aBvcMBZf_8wTlF8,1131
scipy/special/tests/test_spfun_stats.py,sha256=ArofuQVYCm6RevEx0gP2AJ1_6ARrf4Qs5jL3j0LEPKU,2058
scipy/special/tests/test_sph_harm.py,sha256=l8oENVYh0A936bByLfItPR_8vbjys0gmgOKfyGMFRp8,3158
scipy/special/tests/test_spherical_bessel.py,sha256=LZQ9RRNLItd2pem6DH8dThkPRPy1vDR8H-TRlC57X6g,15427
scipy/special/tests/test_support_alternative_backends.py,sha256=4maqGsMzmY763DxJzAnpQTwGhUbUY059bN5TEuKePFg,4535
scipy/special/tests/test_trig.py,sha256=vJL6u-XkIOfMOAAqAUs_xfbcJT4CG3HwF6iZ3fMmgjI,2404
scipy/special/tests/test_ufunc_signatures.py,sha256=s5AFKayOq0iSQ6d8Cwre4AUZI-JyQhgx1SSw4PUJt4w,1884
scipy/special/tests/test_wright_bessel.py,sha256=87ivSP98hCu3rV26blmzvkJ09pCbxNXWk7iud4xFWTM,7899
scipy/special/tests/test_wrightomega.py,sha256=-2-tzEj7HA21xTo_WtCU5nc6rJDT9Tr0Z9IjA2f39mM,3677
scipy/special/tests/test_xsf_cuda.py,sha256=4vOOkrP9JhUmR3U3sT84vrmM5aRWOGo9wgW09bT9a8E,3507
scipy/special/tests/test_zeta.py,sha256=rSSgTb3nhE2pf0PyhqHsc2EU1YxKmYgB3PmGn35i4xg,11850
scipy/special/xsf/binom.h,sha256=Wn0y3Hppi6AlqNPnY6XEDSniXRDtM3o-Fj2SuGE9r68,2560
scipy/special/xsf/cdflib.h,sha256=cPmu6iOwd2G0KXKT7KuGGQZvlkpLbOBinS2CER4gJrk,4276
scipy/special/xsf/cephes/airy.h,sha256=repUhN95UxHHEzR06qYJAkcYwh3JM9pMtx073DDGfjs,11396
scipy/special/xsf/cephes/besselpoly.h,sha256=ScvxsNShyUSmdG6H5jDv_YVRIXtSKZUJ_GQZ5yrzEWQ,1430
scipy/special/xsf/cephes/beta.h,sha256=FJHES0j3xevycRFXn-hU7JS0kTAzj45TquQT7U-2yw8,7238
scipy/special/xsf/cephes/cbrt.h,sha256=DyFPshSmW4u9wftLtbr5GXiFu_r1kyzcWLkOXWbQx4Y,3514
scipy/special/xsf/cephes/chbevl.h,sha256=J4YyOtJTYY_NcVyT8BhEa79h5XGHtN3U5jTWoRgx4rY,1991
scipy/special/xsf/cephes/chdtr.h,sha256=oIAnYI9o-WXfeXbUFcPAEZO8ZeJMdAMVoKf2O7tUPHo,4240
scipy/special/xsf/cephes/const.h,sha256=lwDg8z2oMbeOKYWikuZ-HRmnTJw5YFWo53IJ-yPt_js,3330
scipy/special/xsf/cephes/ellie.h,sha256=_yeCmxo98x2YGCKzi8AcoWnvJgWZwNlBdYpThTOjHS8,9787
scipy/special/xsf/cephes/ellik.h,sha256=HLCfflQ6MqBgX1bfFGpGaU2PJcH8bVarvHFeWpT-QDQ,7852
scipy/special/xsf/cephes/ellpe.h,sha256=lfCfgzNjLsQClzjhs9sEiRPt8Ll8gkn9zzggYSo0B9U,3168
scipy/special/xsf/cephes/ellpk.h,sha256=2DAulnF6vHuk5v_zqWVBmhUtwF0RFHPFH_9VVLCMfBk,3509
scipy/special/xsf/cephes/expn.h,sha256=DS2RiDRO_9VCO_QkMygznQGwTJ1voT28cY7OIyQAGss,9202
scipy/special/xsf/cephes/gamma.h,sha256=TEbPLzE9BbV5xOgp7UiVkAGhS5tMgt7qtNV8ea_lcUI,12402
scipy/special/xsf/cephes/hyp2f1.h,sha256=2hHbWrU6E-MHVElrdhF1kXxunmE4lZWUrZ1wCU4fHSo,20582
scipy/special/xsf/cephes/hyperg.h,sha256=jAeHEfKFEBYTDGR3DastASS2Et3jgPvHQjfUOpDAAe8,10819
scipy/special/xsf/cephes/i0.h,sha256=htbUXYYj_Nr617em7FwzjQ2ZLJBDg2HCfSp_wCXyX70,4697
scipy/special/xsf/cephes/i1.h,sha256=Eq2tEdH6RwpXi42SM2R6aE0YmYFCiEQwfgPeB-ITEbc,4898
scipy/special/xsf/cephes/igam.h,sha256=PJIICZCUrX5Bz741pyR5yZ-FpzKIMNwC6bOt25wBlq8,13298
scipy/special/xsf/cephes/igam_asymp_coeff.h,sha256=hQGBqOP5myfmKK3yAsRIQz6t1BFDDluK2qbTFHKWw-8,18088
scipy/special/xsf/cephes/igami.h,sha256=znQTjwusq6ac7QB0f2F-tSn8z_t3TqcDNuZ6Z8sZL4U,13000
scipy/special/xsf/cephes/j0.h,sha256=Jav_3WUiZ16EiTeuWfUXC5GZltTFTWYg1jyjHTL5x9c,7103
scipy/special/xsf/cephes/j1.h,sha256=NFzb-MBeyfob0XYenIKLyStnzFzile4pPbCDeWbHP_g,6256
scipy/special/xsf/cephes/jv.h,sha256=n4mAtdLawqTp08rLiu7pvHpmxlZIrM9khiMIR2UfcJY,23845
scipy/special/xsf/cephes/k0.h,sha256=N0bxK4bDR5GpL-hQqaZ-zx0pWDVY9z5-1uv7nSDNA5Q,5028
scipy/special/xsf/cephes/k1.h,sha256=efCokjaGpDAPFzOc3kqOA8jSPjvGelsYi83lpIJuVRY,4789
scipy/special/xsf/cephes/kn.h,sha256=c3bJMcVHsSO0tmNn07z3bGK_SPbdX0wZvKvPwK5kqnA,6507
scipy/special/xsf/cephes/lanczos.h,sha256=t1EBcg3GdvxtpocpVBRqixYV6Bz2NhTpS1SJbujEMkE,5606
scipy/special/xsf/cephes/ndtr.h,sha256=Loqcw1_T0C-Iw8EiRIvQf8PkBy7Im30JQf0pYaqyPSA,6956
scipy/special/xsf/cephes/poch.h,sha256=4VEabdyY0emBI4bCTqWK4S3LynD4di-p_vcEceHMKuQ,2468
scipy/special/xsf/cephes/polevl.h,sha256=gijaeMdJNkBeDpdCtofBhG0i3HgsWU39zypr4DMWaeQ,4242
scipy/special/xsf/cephes/psi.h,sha256=lhw-40lXacBRU0YfCAPWepRgdG6rVEx-c48DDjoBOk0,6485
scipy/special/xsf/cephes/rgamma.h,sha256=y1LB_FCpwC6gZwNoElmFmeB5VbnCxeMkwdKs5lHa_G0,3169
scipy/special/xsf/cephes/scipy_iv.h,sha256=CqT8Wh9xdfbBKXIMyoh4TADnhefvDr0AnDlXdsjTUGo,26261
scipy/special/xsf/cephes/shichi.h,sha256=WbNfIyD3UzqEU15pGRtPx-iaaBafyHmpVHf2FtDvxwM,8761
scipy/special/xsf/cephes/sici.h,sha256=CpI8ngwTCcSaOxoKLLUacx3sRBTXQCfAmQOpy0Dbir8,7549
scipy/special/xsf/cephes/sindg.h,sha256=cgXYuCYAFEbF42nVHaqL2qmXW_ielGnT5Ne_26RisVo,5855
scipy/special/xsf/cephes/tandg.h,sha256=CHUgAH6uKU4XaFXSZym8cyE9Z0DMhjxQkRVThhVE-24,3530
scipy/special/xsf/cephes/trig.h,sha256=FBH5SwiFjJKmofDFMcSPLUG9izdux4ZgG6V4eR76yW4,1398
scipy/special/xsf/cephes/unity.h,sha256=-svrNq3gTqRESDAV-DDX7BzrojqqX7lZZGPli_Y2F7Y,5239
scipy/special/xsf/cephes/zeta.h,sha256=vFBawY5wd2G74t4VAlVadBx3bb1xzc4eVlBLkgORCZ8,4553
scipy/special/xsf/config.h,sha256=tZ_Z2UbWeMM9-g66ZIHfV9cJgD0j9v64kz2XfY4Az5w,8742
scipy/special/xsf/digamma.h,sha256=ywaP6LsUqHiy2blM4SSeHBOV6Vgo-FytLftIa6MfeAc,7720
scipy/special/xsf/error.h,sha256=JI8cgddpUssKY4pw42BdyJjn1dw6Z6Uu9kLEppetyMg,1788
scipy/special/xsf/evalpoly.h,sha256=nLlfvlRzRKcrycqvYyzcKHkuxOhK7bvxn2a8dhtlc1w,1166
scipy/special/xsf/expint.h,sha256=kS8EwIxn_L3BmS_0VTkhmmeEqPpseszMx9ikLYsghaw,8611
scipy/special/xsf/hyp2f1.h,sha256=dUHptebetIrabSLjKJYXPKxXr4ahTRa0777HVOr4z0E,35432
scipy/special/xsf/iv_ratio.h,sha256=APds-YuLVlFXxzZ9GK7jQ5eFU8KM7u1LzXT_NCx74cI,5847
scipy/special/xsf/lambertw.h,sha256=gX6cu35zxrkTDhOIPzXdXm2QXwO73wvLdN80idpYO_4,5561
scipy/special/xsf/loggamma.h,sha256=u-yVFgtvsrM62tqIwJB7AKcCke_zh5m31VZVNYBDVbE,6372
scipy/special/xsf/sici.h,sha256=Cac31oZiioaVhl3ifpa7Rf8ECKeuSayF2cNXLv9_c1o,6054
scipy/special/xsf/tools.h,sha256=iaWRJhazHsolWud7plXjkpRWGUD_62qjT4tNrkkIpGs,16572
scipy/special/xsf/trig.h,sha256=wt1WfA6vJ0LXA7WrS3j9PI0V9h4BLA1Cnt2IsLppxV4,4526
scipy/special/xsf/wright_bessel.h,sha256=Rocr9gpaG11BKE2XHvRVgsGy1aAqnrQIkd0xowXwg-k,43462
scipy/special/xsf/zlog1.h,sha256=GpCB0AvDygyz60k2jKOt1oiJ6jlx8IDOdosiCv9foCg,1000
scipy/stats/__init__.py,sha256=euBPlzy1z5K6pcuhemQgLAKIS8LutbHNhYj6Sca-ACM,19347
scipy/stats/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/__pycache__/_axis_nan_policy.cpython-311.pyc,,
scipy/stats/__pycache__/_binned_statistic.cpython-311.pyc,,
scipy/stats/__pycache__/_binomtest.cpython-311.pyc,,
scipy/stats/__pycache__/_bws_test.cpython-311.pyc,,
scipy/stats/__pycache__/_censored_data.cpython-311.pyc,,
scipy/stats/__pycache__/_common.cpython-311.pyc,,
scipy/stats/__pycache__/_constants.cpython-311.pyc,,
scipy/stats/__pycache__/_continuous_distns.cpython-311.pyc,,
scipy/stats/__pycache__/_correlation.cpython-311.pyc,,
scipy/stats/__pycache__/_covariance.cpython-311.pyc,,
scipy/stats/__pycache__/_crosstab.cpython-311.pyc,,
scipy/stats/__pycache__/_discrete_distns.cpython-311.pyc,,
scipy/stats/__pycache__/_distn_infrastructure.cpython-311.pyc,,
scipy/stats/__pycache__/_distr_params.cpython-311.pyc,,
scipy/stats/__pycache__/_distribution_infrastructure.cpython-311.pyc,,
scipy/stats/__pycache__/_entropy.cpython-311.pyc,,
scipy/stats/__pycache__/_fit.cpython-311.pyc,,
scipy/stats/__pycache__/_hypotests.cpython-311.pyc,,
scipy/stats/__pycache__/_kde.cpython-311.pyc,,
scipy/stats/__pycache__/_ksstats.cpython-311.pyc,,
scipy/stats/__pycache__/_mannwhitneyu.cpython-311.pyc,,
scipy/stats/__pycache__/_mgc.cpython-311.pyc,,
scipy/stats/__pycache__/_morestats.cpython-311.pyc,,
scipy/stats/__pycache__/_mstats_basic.cpython-311.pyc,,
scipy/stats/__pycache__/_mstats_extras.cpython-311.pyc,,
scipy/stats/__pycache__/_multicomp.cpython-311.pyc,,
scipy/stats/__pycache__/_multivariate.cpython-311.pyc,,
scipy/stats/__pycache__/_new_distributions.cpython-311.pyc,,
scipy/stats/__pycache__/_odds_ratio.cpython-311.pyc,,
scipy/stats/__pycache__/_page_trend_test.cpython-311.pyc,,
scipy/stats/__pycache__/_probability_distribution.cpython-311.pyc,,
scipy/stats/__pycache__/_qmc.cpython-311.pyc,,
scipy/stats/__pycache__/_qmvnt.cpython-311.pyc,,
scipy/stats/__pycache__/_relative_risk.cpython-311.pyc,,
scipy/stats/__pycache__/_resampling.cpython-311.pyc,,
scipy/stats/__pycache__/_result_classes.cpython-311.pyc,,
scipy/stats/__pycache__/_sampling.cpython-311.pyc,,
scipy/stats/__pycache__/_sensitivity_analysis.cpython-311.pyc,,
scipy/stats/__pycache__/_stats_mstats_common.cpython-311.pyc,,
scipy/stats/__pycache__/_stats_py.cpython-311.pyc,,
scipy/stats/__pycache__/_survival.cpython-311.pyc,,
scipy/stats/__pycache__/_tukeylambda_stats.cpython-311.pyc,,
scipy/stats/__pycache__/_variation.cpython-311.pyc,,
scipy/stats/__pycache__/_warnings_errors.cpython-311.pyc,,
scipy/stats/__pycache__/_wilcoxon.cpython-311.pyc,,
scipy/stats/__pycache__/biasedurn.cpython-311.pyc,,
scipy/stats/__pycache__/contingency.cpython-311.pyc,,
scipy/stats/__pycache__/distributions.cpython-311.pyc,,
scipy/stats/__pycache__/kde.cpython-311.pyc,,
scipy/stats/__pycache__/morestats.cpython-311.pyc,,
scipy/stats/__pycache__/mstats.cpython-311.pyc,,
scipy/stats/__pycache__/mstats_basic.cpython-311.pyc,,
scipy/stats/__pycache__/mstats_extras.cpython-311.pyc,,
scipy/stats/__pycache__/mvn.cpython-311.pyc,,
scipy/stats/__pycache__/qmc.cpython-311.pyc,,
scipy/stats/__pycache__/sampling.cpython-311.pyc,,
scipy/stats/__pycache__/stats.cpython-311.pyc,,
scipy/stats/_ansari_swilk_statistics.cp311-win_amd64.dll.a,sha256=MvJkaUDMOAf6SGOlI7d24A5B4vSq7XlSNEhaPrlmQkA,1752
scipy/stats/_ansari_swilk_statistics.cp311-win_amd64.pyd,sha256=TnJivfKDfQBO-2LfLYWu3Xl4hQH0gUAf-f3MQiZ51i0,257536
scipy/stats/_axis_nan_policy.py,sha256=3wmJWjbaraZKTttW4NHL95tAHKo8ukrb48mxN9j-HII,32487
scipy/stats/_biasedurn.cp311-win_amd64.dll.a,sha256=nmAnyZhLE1p6fv25ERvLSE_jEhch7ETOqizChMMQlAE,1580
scipy/stats/_biasedurn.cp311-win_amd64.pyd,sha256=fDhWOPe1xxT9buBCuIC3mZINzKfPCdCfz8zDOSDPQWM,358912
scipy/stats/_biasedurn.pxd,sha256=OHa5weTjmvbIdkvmgDnD021p44bVaS5mNvDIx562SLI,1073
scipy/stats/_binned_statistic.py,sha256=kq_rKOZO_Uapc-4MtRasQxdoLOMzYw-bc9jv9eQrFyc,33497
scipy/stats/_binomtest.py,sha256=gXacpbMuFCBgpwG_1__1InZhMgZsPfDZ3FFETkPybQQ,13493
scipy/stats/_bws_test.py,sha256=h-6Ra_vazN0T6FA1kHZkG1AkEQ6JiRkFzJwVxD5ghS8,7239
scipy/stats/_censored_data.py,sha256=-mOWeG-hGYqCz8sjbELiBeBOE216wS8PsvmD99YNVxA,18765
scipy/stats/_common.py,sha256=PUBtrtKESxYoaVx3tlq8ZYImEYCIuYslFYikUS7uwdU,177
scipy/stats/_constants.py,sha256=X7Fo6g5foBhU1ldV3ym3DE8uloF050eZsSw_4MSw3ls,1044
scipy/stats/_continuous_distns.py,sha256=nXYR-Ov-nrg5K9bspQO3deZTbXhrwi7Q-A1sJ8985iA,420201
scipy/stats/_correlation.py,sha256=DLcWZNg8HSMsN9SfUTxuf7UlUgCMUa6HdXbn42Y0L20,8121
scipy/stats/_covariance.py,sha256=quuUd1AqbdCW9b83koE_XC40nAxhtlAJ4M5O8OqLrYg,23157
scipy/stats/_crosstab.py,sha256=-qNk2ozojQ2Zd94JYZ5eRcB3kT-GjR26WCP6DbbGk4g,7555
scipy/stats/_discrete_distns.py,sha256=itRGBv7ezC6ZzHBR_M7rLZEkLO2o5A0nEoFXRetOr5U,67186
scipy/stats/_distn_infrastructure.py,sha256=oIV_h1K3fFdEnvCU7rNcZdJQTHMr0ZaeZ4GKf0iHSaY,155762
scipy/stats/_distr_params.py,sha256=zwtg0mBOTQxKLElc2wfKouUVHVws5kDgq-2qe1YUAg4,9351
scipy/stats/_distribution_infrastructure.py,sha256=CRXbb04BIjj5jWkEEj1rQxYAC9-imrt9uj3-ed_dihE,208840
scipy/stats/_entropy.py,sha256=MloB2zD2ZbrfW0Df9XJWImVudcr65_hzavSBIFUTAjw,16260
scipy/stats/_fit.py,sha256=b9q830FSqMvz-LCEG0vSkwXN4Scr6G6pnrSRksLvA5Q,61098
scipy/stats/_hypotests.py,sha256=dOOHDVhNQzkeT8QSBqilm1Mb27PK389VivCcuUznWQ4,81197
scipy/stats/_kde.py,sha256=4IcX7eFv6-TTdPITSANvrmpHOfDeiPoy6cDooJetaDk,25817
scipy/stats/_ksstats.py,sha256=PHhHzxYHO9O-SihuSQ9X6__wnNbtv7FvKVVpf_FsKaE,20739
scipy/stats/_levy_stable/__init__.py,sha256=9LHLpYD_uSqiY0i5F_Z_rUPD3WJy6IFN04xa7XQf90w,47225
scipy/stats/_levy_stable/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/_levy_stable/levyst.cp311-win_amd64.dll.a,sha256=ypV5caGwpT15Ejjv9LrXvV8jUDeAWeJLl1dB997h58o,1532
scipy/stats/_levy_stable/levyst.cp311-win_amd64.pyd,sha256=GDTN2xUuzUcFuEzcHUD-fHfRM37SKAMu76a2cOndNVk,56320
scipy/stats/_mannwhitneyu.py,sha256=FqQBZYBT7vi-6iSHB8oDTzyqhSFrHFm6RuXT1Br47dI,19822
scipy/stats/_mgc.py,sha256=_yzzzEi9nPaFV6pZE1_epNRg5mYzssDRuF7F_DN_eQY,21909
scipy/stats/_morestats.py,sha256=gXF3LBtm5tCZstiHw6n7sKDX0tOD-G0wdxoQ0NP935Y,174974
scipy/stats/_mstats_basic.py,sha256=1oIqCh59YuBbnzlCXhRRAoYft5ofzHvk2g53z_t2bWg,126601
scipy/stats/_mstats_extras.py,sha256=mPZcITM6fNoQdwi-xIysdFfhQlRKjB2joMPyYPvFV2M,16887
scipy/stats/_multicomp.py,sha256=w0MZm7pJPB0rQYYtloKNS6bxPcGD7PbgTBtj7WFDVos,17285
scipy/stats/_multivariate.py,sha256=x3iUcTBy13ddSHAVZvISUc5JrebvsJpnw-egWnQhLG4,256545
scipy/stats/_mvn.cp311-win_amd64.dll.a,sha256=u54RzTLX9DTqo4fiDwLCx6EI4d1_IVukb74CiYIWIfI,1512
scipy/stats/_mvn.cp311-win_amd64.pyd,sha256=sUfSx3PUcIrDCiFz9gtejbfvTBS04etxQGqlu4tdAHw,105984
scipy/stats/_new_distributions.py,sha256=L7STk7qnvrtypKz5H0TS3BoaLm4XJT1JZzAFtNWLtMA,13614
scipy/stats/_odds_ratio.py,sha256=KL3uWWfvfbnEwEkqRiUZJzOr_NtvAy09hvN3dVM8dZw,17471
scipy/stats/_page_trend_test.py,sha256=dvnRV0v6KZvrZyLT3w7nkp02dkv5T54C4ZcTDFt-iy0,19466
scipy/stats/_probability_distribution.py,sha256=FDGLma_JRMOyffTMwsaVDPHxqmYwwKA5LaE5vKybdlA,63246
scipy/stats/_qmc.py,sha256=3r7P1QtVm18TvuhU8drIZzvLA2lwCW86CHuPLrvXJ9g,110453
scipy/stats/_qmc_cy.cp311-win_amd64.dll.a,sha256=a1_69whXJEJkwhe9rjIZgjhpnZXHG17okQlhT9J9YrA,1544
scipy/stats/_qmc_cy.cp311-win_amd64.pyd,sha256=Mjw3c8H6_MvA6bvwuBGAuyGXVfdLgFPro7Lu6s-NyR0,407552
scipy/stats/_qmc_cy.pyi,sha256=l2173UiDribq_G9S47QozpcKo8nO9D3rojwKLEd8m8k,1188
scipy/stats/_qmvnt.py,sha256=2cbAYoC7AqpWHZvZOREY84_VDlP2gHQ14UBaECVy3Xc,19302
scipy/stats/_rcont/__init__.py,sha256=xtM2CGKxJ2uUfF6Pfyoi_0em8i5pa6njGRaaGSrvFkA,88
scipy/stats/_rcont/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/_rcont/rcont.cp311-win_amd64.dll.a,sha256=nKKa0T4NIwKRGKm_zemb38PS2pva-9k0b-py2N3H67o,1520
scipy/stats/_rcont/rcont.cp311-win_amd64.pyd,sha256=5cJ3v-vPEMpW56oFloUtzHcTr3B_h8RiDqGQ8IASejM,237568
scipy/stats/_relative_risk.py,sha256=txlXt8-p2wLydMIrKEOwdAt5giral_X51T2itd7VDDw,9834
scipy/stats/_resampling.py,sha256=_7uPMouRXnj2RcWMWAsYqrA1ugRnwsND5QsW3arklLc,106672
scipy/stats/_result_classes.py,sha256=904WSGrKwzuWHdd3MaS-HUwTJzLm8iF5aNzh0wZq-TM,1125
scipy/stats/_sampling.py,sha256=6pcrRPLvRFI-VRM4gRuw8Fub2l8Ot8WeyC-o_AEM3KA,47722
scipy/stats/_sensitivity_analysis.py,sha256=lTO1lFNHf0zpLA96VUXvaUmd53l2Qh-ymV96JyzqJGo,25754
scipy/stats/_sobol.cp311-win_amd64.dll.a,sha256=m8d76x8PumDLJdkbG3Xen8bGs79ND_OccCDE4KrKiak,1532
scipy/stats/_sobol.cp311-win_amd64.pyd,sha256=zPIJ0Oz-jqZnkT5uaRFZRZ45GAOsICIHj667B5-IWEs,374272
scipy/stats/_sobol.pyi,sha256=tskim056aVi6WIymUMEMdWHaLk8QMqWa2kCAbF2ZTPo,1025
scipy/stats/_sobol_direction_numbers.npz,sha256=SFmTEUfULORluGBcsnf5V9mLg50DGU_fBleTV5BtGTs,589334
scipy/stats/_stats.cp311-win_amd64.dll.a,sha256=9fiSmRD9ymfLE3KlrMwUuGchTYYI2vRN0cKEmzdo9Rw,1532
scipy/stats/_stats.cp311-win_amd64.pyd,sha256=40a6r9vP-SKGNTf3xpyEkVV2IOa1-IQauw7UxSSAQag,693248
scipy/stats/_stats.pxd,sha256=EU9o8xwL148qbx8h0aI2Uy-lUtZ3i683HIRZRkTvjHM,719
scipy/stats/_stats_mstats_common.py,sha256=uzD2i2higUX8-we7sMBJ1RLGuIQeJ0FMglwvOm0d2sM,11860
scipy/stats/_stats_py.py,sha256=WTfTv4cmewF2ugf_3aGHTpMyZmVu06GA4Ow1f0vsJso,428532
scipy/stats/_stats_pythran.cp311-win_amd64.dll.a,sha256=bLiKwyCmMXzjLO2CNK-0v-VUyD_VQrDbo4Opq6Z90YY,1628
scipy/stats/_stats_pythran.cp311-win_amd64.pyd,sha256=bn_kJGjE1R55wpkN5FvfWLIpjpMrwDUg3lmhzYQktPg,1086464
scipy/stats/_survival.py,sha256=t6IJKabL1FqmWKtx37Ve45SuviGjYVW5E6-WRQLlZwk,26622
scipy/stats/_tukeylambda_stats.py,sha256=RtXsm72WCNJ8kQSYkL6x5XEsRopLVkY_wUSS_32NtmY,7070
scipy/stats/_unuran/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/_unuran/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/_unuran/unuran_wrapper.cp311-win_amd64.dll.a,sha256=_mpGqZT53jOBKEE6AXtoss4ZKFKXMyS9VLirBcn2OVc,1628
scipy/stats/_unuran/unuran_wrapper.cp311-win_amd64.pyd,sha256=K9uI4xzkaVkeTiz31g7fbElGLZxOkaZC6dNcIVs-g4Y,1471488
scipy/stats/_unuran/unuran_wrapper.pyi,sha256=d830ufTp2lbknc01d61q-prLLdcsJLn5oaEjHRrUK3s,5766
scipy/stats/_variation.py,sha256=M230FqSkmVzriVd-WEQx5SxW9baxGRsVj4F8L-G6zAE,4780
scipy/stats/_warnings_errors.py,sha256=f_Hxg3GzZJW2U1B608HFABSkV2w9Xjv1qtsND2AuGKA,1234
scipy/stats/_wilcoxon.py,sha256=Ws4vmCLAnH6SkA2hEKh8u_ENNKWzHpUcRtdS9KdeBbE,9641
scipy/stats/biasedurn.py,sha256=gb1UJyRCcvPzqliBEPcPBYUPrIha8ztIOXphhQc6QG4,447
scipy/stats/contingency.py,sha256=tRU6tvs3pDP9WI9X1IrZqThUAnaFtfRWFmtPFD8x3zQ,19170
scipy/stats/distributions.py,sha256=_nRpDudL-PbnWvNm3fxRQ-00zEtCleSqhTNk1evZvq8,883
scipy/stats/kde.py,sha256=CtU4WVO1iuK6f80cn8Lz8z75OXwE0z4BevdRy_o_bP4,534
scipy/stats/morestats.py,sha256=2qX7gBOG7_3PqAtwLS0uM1198ehUm8z8u8W2pikxaYs,1000
scipy/stats/mstats.py,sha256=jzvELjOG5FulzAORL52iNL6VwQhA3N27RAr_-8fSu1Y,2606
scipy/stats/mstats_basic.py,sha256=4GSiBxQUMXnsBtFoV8BW6etxocpU69xfLzLFHO1gt9c,1436
scipy/stats/mstats_extras.py,sha256=OEMN7L4PfGARho8qIF9fmUg6TXnQcWBVWBHLeZ7m1l0,746
scipy/stats/mvn.py,sha256=WopFz0qBdEbGzhKk7eGfFVa9ojHkg2aKn3nzOjAkq4s,515
scipy/stats/qmc.py,sha256=tm9AkmRLZQCSc9HDwXywCd4b_lcxqQZbeRsNtqTWfEY,11939
scipy/stats/sampling.py,sha256=DB2ePAYSuu7z4FH2CcSSW9ifDWTQi8q1lTbpNitCxCY,2012
scipy/stats/stats.py,sha256=N2DCCNuXdZJ_EKuON0UCUF1gu_S8p20PEaJTCfoPfg8,1553
scipy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
scipy/stats/tests/__pycache__/__init__.cpython-311.pyc,,
scipy/stats/tests/__pycache__/common_tests.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_axis_nan_policy.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_binned_statistic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_censored_data.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_contingency.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_continuous.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_continuous_basic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_continuous_fit_censored.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_correlation.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_crosstab.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_discrete_basic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_discrete_distns.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_distributions.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_entropy.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_fast_gen_inversion.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_fit.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_hypotests.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_kdeoth.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_mgc.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_morestats.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_mstats_basic.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_mstats_extras.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_multicomp.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_multivariate.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_odds_ratio.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_qmc.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_rank.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_relative_risk.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_resampling.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_sampling.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_sensitivity_analysis.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_stats.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_survival.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_tukeylambda_stats.cpython-311.pyc,,
scipy/stats/tests/__pycache__/test_variation.cpython-311.pyc,,
scipy/stats/tests/common_tests.py,sha256=06vfUjHmE_Hbq_valQIajff0Nd1w_JgsfD6X6J_EXCE,12788
scipy/stats/tests/data/__pycache__/_mvt.cpython-311.pyc,,
scipy/stats/tests/data/__pycache__/fisher_exact_results_from_r.cpython-311.pyc,,
scipy/stats/tests/data/_mvt.py,sha256=FhbZqSVqMei58-awIxMScB_gSDj_1Fy-RKRkJwuBBns,7076
scipy/stats/tests/data/fisher_exact_results_from_r.py,sha256=AqKIctwYj-6FUc6XhwZIfrPCEw073oMKhSWrDhuz00I,27956
scipy/stats/tests/data/jf_skew_t_gamlss_pdf_data.npy,sha256=JU0t7kpNVHuTMcYCQ8b8_K_9JsixBNCNT2BFp2RbO7o,4064
scipy/stats/tests/data/levy_stable/stable-Z1-cdf-sample-data.npy,sha256=zxjB8tZaIyvyxxISgt8xvyqL6Cevr8TtgQ7TdFfuiYo,183728
scipy/stats/tests/data/levy_stable/stable-Z1-pdf-sample-data.npy,sha256=_umVErq0zMZWm0e5JOSwNOHNurViT6_H4SBki9X3oSg,183688
scipy/stats/tests/data/levy_stable/stable-loc-scale-sample-data.npy,sha256=88cZ7dVDH7nnuey20Z48p6kJUpi9GfImaFsPykDwwHM,9328
scipy/stats/tests/data/nist_anova/AtmWtAg.dat,sha256=WhCDyQYtOWbyRL_6N2_JsDMkSr-GoNDdAWrznNcfuKc,3171
scipy/stats/tests/data/nist_anova/SiRstv.dat,sha256=Wk8C1_JW4Yww3K0bgeAmC7gQtawRP2EH4G3uFo0HZj4,2032
scipy/stats/tests/data/nist_anova/SmLs01.dat,sha256=rSjKwNT7ef_qJSK1U0kIO96hDxKm-MRs-6vMzbTWuJY,6304
scipy/stats/tests/data/nist_anova/SmLs02.dat,sha256=cI4y6vNnaavnUz59zRttxnCRtg35ON03xPDmVmDvews,48430
scipy/stats/tests/data/nist_anova/SmLs03.dat,sha256=4YQE0GKLeGcFgLIwo6O2zZl9lZ0A-Soog6TalCsZN3o,469635
scipy/stats/tests/data/nist_anova/SmLs04.dat,sha256=EwjrFYHyjObJ3kypUg_Mcp-RbDTx-d7hD7bmWB5bTOc,7064
scipy/stats/tests/data/nist_anova/SmLs05.dat,sha256=exMz5uVs7XQUUJbfd0z7H2MFITyyj5qCIsQYyoXQwpY,55668
scipy/stats/tests/data/nist_anova/SmLs06.dat,sha256=DA8Jot8F8unbUySmqu7YVkdlHodOn1w4krjyn8SiAPc,541674
scipy/stats/tests/data/nist_anova/SmLs07.dat,sha256=_ZXFEKqLPOf2fd0yhGzOrX2auuRBeQM2xvsPywrjVY8,7630
scipy/stats/tests/data/nist_anova/SmLs08.dat,sha256=h9043__rZdOdvj6LpzvRZkT8OWwarQP8ApUiXzumrvk,61097
scipy/stats/tests/data/nist_anova/SmLs09.dat,sha256=9GiMWg1WkPyG-ifgxaJl7vCyRkHnSvJ6py2WFQhN0T8,595702
scipy/stats/tests/data/nist_linregress/Norris.dat,sha256=WraQbGipxLLrFmHAzX0qCfWkaabU4FtraR8ZD197lE8,2688
scipy/stats/tests/data/rel_breitwigner_pdf_sample_data_ROOT.npy,sha256=7vTccC3YxuMcGMdOH4EoTD6coqtQKC3jnJrTC3u4520,38624
scipy/stats/tests/data/studentized_range_mpmath_ref.json,sha256=ZIARw6MmSmQkPpmLKGurnF_WxcZqQxUJmgoVsooTJIU,30737
scipy/stats/tests/test_axis_nan_policy.py,sha256=1WZAwbAdjrwgg4ndpdLSwzATIPFwGDTUGzqnmr-yzyU,59917
scipy/stats/tests/test_binned_statistic.py,sha256=Zx8gdkQGNDoF6gUXLbi3PJdXsN-4ZmVrB_4HnCcYYYY,19382
scipy/stats/tests/test_censored_data.py,sha256=LtTBWL6HY-0m7HzbZkYDLO1NH3Z0h_D7I2XZ27CwuWY,7087
scipy/stats/tests/test_contingency.py,sha256=ix_2PGZGRxzxVZEDbc_GsNhRaeNwk69Ntu6JkcCspjQ,11231
scipy/stats/tests/test_continuous.py,sha256=49J1Hg61zFH4uW0e0xEc4AvDnq-YaGJ13UGOAdP8-a0,81249
scipy/stats/tests/test_continuous_basic.py,sha256=F7ET4PeW7aJSMMEL1zmg46Dbwk09yNg_IKMaWTx2YwE,43925
scipy/stats/tests/test_continuous_fit_censored.py,sha256=PtKzuvJPtFTF__pr209v-i34pcYOXSCB1vMfz8Uez3M,24871
scipy/stats/tests/test_correlation.py,sha256=ecJnDF84nPWHg43aEN6DMMsp46g8tQ5aeVz0ErUihLg,3587
scipy/stats/tests/test_crosstab.py,sha256=mfrrI2cyyW5XpSrFpGI7oqcSBfbzxVJkPCv1TOO9NoU,4021
scipy/stats/tests/test_discrete_basic.py,sha256=yRhwZDa7JsfYhqsZKEaDHQ4OUZnwEEmwBkbpJi-625U,21596
scipy/stats/tests/test_discrete_distns.py,sha256=ExzGvLGk66lEgM9nY4bVOlUHDStsR0J462jVN6bC6jk,25961
scipy/stats/tests/test_distributions.py,sha256=WBb4K0Kj98yN5oAINmKnzjjOxxdblzwSTANFkQMAgL4,422506
scipy/stats/tests/test_entropy.py,sha256=YajFPoUoQEsHWMWpwwdSCbpNwS0Z2LAKcRX8zUudrBw,14283
scipy/stats/tests/test_fast_gen_inversion.py,sha256=V3OJ8ppJ3OfkFd1ABkSiqZqalvIq9Sl_PKy4xJ3jGPU,16368
scipy/stats/tests/test_fit.py,sha256=On_yiY2GoDVMpxQDdniywd9dyn5aMM1Ej_EHxye7f2U,49964
scipy/stats/tests/test_hypotests.py,sha256=wK1-rW2_HHUuUlwvWS0341gCZkynf7WR6vbytw5kr3Q,81840
scipy/stats/tests/test_kdeoth.py,sha256=Fx5W7-W43KrxujO6PJ2pKUDznsiwjxdG4KYfPnqxj2s,21081
scipy/stats/tests/test_mgc.py,sha256=_lAD5mNMBScEjkPpCJNMWrhZNClHwW3pHtFMR0YNu4o,8178
scipy/stats/tests/test_morestats.py,sha256=z2u7mTZtoqLzJR_MgbUwpj9i4FQXnDB_5z7tIl_BLmw,144674
scipy/stats/tests/test_mstats_basic.py,sha256=D06il3VMYysbMr3CXZ-JGuOwoflQBBVYITBkxQ1tDR4,89364
scipy/stats/tests/test_mstats_extras.py,sha256=ae8Qn_-iLAI70UaMDbl_r78eeX2xVShuNrRXa81jMxA,7469
scipy/stats/tests/test_multicomp.py,sha256=ILxEEqaRleWZz200whB8F9TH9wKB5hEmprOc9ACwhzs,18231
scipy/stats/tests/test_multivariate.py,sha256=fRDo0WPLSQvg8D6Z7Eqv3KuVqAv_bDVLbMjvr-sxNwE,164320
scipy/stats/tests/test_odds_ratio.py,sha256=UtuPRXBu9QTZ-gMynkqB1XxqJs0A3ldUBPh0wmEcjww,6875
scipy/stats/tests/test_qmc.py,sha256=P6hHbmKh7xF9sEdW17mRnYdYvy8BmTfKDirFpqYQyKE,59018
scipy/stats/tests/test_rank.py,sha256=aAHd5i2KFh0i-urgQW7uw6bLIkdMwQEbDvPcoLIHdjw,12131
scipy/stats/tests/test_relative_risk.py,sha256=_JizDcNuNxqEQ5j9fNqbvhu0Xxa8YkDUlBivVTsykhY,3741
scipy/stats/tests/test_resampling.py,sha256=v1VEllgPHaIteUV5HVZKH0tt594-aKuNChBWkTEWWTI,84438
scipy/stats/tests/test_sampling.py,sha256=7DQZ7TcZOS5Y4ZD4S68NJedyCBR-yY1KXY6BD5ny6x0,55985
scipy/stats/tests/test_sensitivity_analysis.py,sha256=WxCMsbluDeFPFUvIKf7DudvtRVdMHSMmzymPIyZoBSs,10988
scipy/stats/tests/test_stats.py,sha256=FXG8CD59bfJGv9dpUU9qFbAxA-dSRN2YuJz6t_XZwAI,423533
scipy/stats/tests/test_survival.py,sha256=bO_oeXtEdpkMDEpralDhflpqVxiEOWwUnuqUcMQ1oTA,22424
scipy/stats/tests/test_tukeylambda_stats.py,sha256=AmqMknbKki17oYdfP5H_IaP6WyuvWRXNyUrWflzGLAE,3316
scipy/stats/tests/test_variation.py,sha256=dYJ2KKIaPxH1Mqox8EhPnaIn_NDKvqcSVih8hGVwCFQ,9416
scipy/version.py,sha256=Jzj9aTD0bFl1IiiifDRY943DefYO-vb4u00NC2nUsuA,330
